'use client';

import { useState } from 'react';
import { 
  Mail, 
  Lock, 
  Eye, 
  EyeOff, 
  Shield, 
  ArrowRight,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

export default function LoginPage() {
  const [isLogin, setIsLogin] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    phone: '',
    agreeToTerms: false
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // إزالة الخطأ عند التعديل
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    // التحقق من البريد الإلكتروني
    if (!formData.email) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }

    // التحقق من كلمة المرور
    if (!formData.password) {
      newErrors.password = 'كلمة المرور مطلوبة';
    } else if (formData.password.length < 8) {
      newErrors.password = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }

    // التحقق من الحقول الإضافية للتسجيل
    if (!isLogin) {
      if (!formData.fullName) {
        newErrors.fullName = 'الاسم الكامل مطلوب';
      }

      if (!formData.phone) {
        newErrors.phone = 'رقم الهاتف مطلوب';
      } else if (!/^[0-9+\-\s()]+$/.test(formData.phone)) {
        newErrors.phone = 'رقم الهاتف غير صحيح';
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'كلمات المرور غير متطابقة';
      }

      if (!formData.agreeToTerms) {
        newErrors.agreeToTerms = 'يجب الموافقة على الشروط والأحكام';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    // محاكاة عملية تسجيل الدخول/التسجيل
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (isLogin) {
        alert('تم تسجيل الدخول بنجاح!');
      } else {
        alert('تم إنشاء الحساب بنجاح! يرجى تأكيد البريد الإلكتروني.');
      }
    } catch (error) {
      alert('حدث خطأ، يرجى المحاولة مرة أخرى');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* خلفية زخرفية */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-40 h-40 bg-blue-500 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-60 h-60 bg-purple-500 rounded-full filter blur-3xl"></div>
      </div>

      <div className="max-w-md w-full space-y-8 relative z-10">
        {/* الهيدر */}
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg transform hover:rotate-6 transition-transform duration-300">
              <span className="text-white font-bold text-3xl">إ</span>
            </div>
          </div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
            {isLogin ? 'تسجيل الدخول' : 'إنشاء حساب جديد'}
          </h2>
          <p className="mt-2 text-gray-600">
            {isLogin
              ? 'ادخل إلى حسابك للوصول إلى منصة إيكاروس P2P'
              : 'انضم إلى آلاف المستخدمين الذين يثقون في منصتنا'
            }
          </p>
        </div>

        {/* النموذج */}
        <form className="mt-8 space-y-6 bg-white p-8 rounded-2xl shadow-xl border border-gray-100" onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* الاسم الكامل - للتسجيل فقط */}
            {!isLogin && (
              <div>
                <label className="form-label">الاسم الكامل</label>
                <input
                  type="text"
                  name="fullName"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  className={`form-input ${errors.fullName ? 'border-danger-500' : ''}`}
                  placeholder="أدخل اسمك الكامل"
                />
                {errors.fullName && (
                  <p className="mt-1 text-sm text-danger-600 flex items-center">
                    <AlertCircle className="w-4 h-4 ml-1" />
                    {errors.fullName}
                  </p>
                )}
              </div>
            )}

            {/* البريد الإلكتروني */}
            <div>
              <label className="form-label">البريد الإلكتروني</label>
              <div className="input-container input-with-icon">
                <Mail className="input-icon-right w-5 h-5" />
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`form-input form-input-with-icon ${errors.email ? 'border-danger-500' : ''}`}
                  placeholder="أدخل بريدك الإلكتروني"
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-danger-600 flex items-center">
                  <AlertCircle className="w-4 h-4 ml-1" />
                  {errors.email}
                </p>
              )}
            </div>

            {/* رقم الهاتف - للتسجيل فقط */}
            {!isLogin && (
              <div>
                <label className="form-label">رقم الهاتف</label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className={`form-input ${errors.phone ? 'border-danger-500' : ''}`}
                  placeholder="+966 50 123 4567"
                />
                {errors.phone && (
                  <p className="mt-1 text-sm text-danger-600 flex items-center">
                    <AlertCircle className="w-4 h-4 ml-1" />
                    {errors.phone}
                  </p>
                )}
              </div>
            )}

            {/* كلمة المرور */}
            <div>
              <label className="form-label">كلمة المرور</label>
              <div className="input-container input-with-icon has-left-icon">
                <Lock className="input-icon-right w-5 h-5" />
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className={`form-input form-input-with-icon ${errors.password ? 'border-danger-500' : ''}`}
                  placeholder="أدخل كلمة المرور"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="input-icon-left text-gray-400 hover:text-gray-600 cursor-pointer"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-danger-600 flex items-center">
                  <AlertCircle className="w-4 h-4 ml-1" />
                  {errors.password}
                </p>
              )}
            </div>

            {/* تأكيد كلمة المرور - للتسجيل فقط */}
            {!isLogin && (
              <div>
                <label className="form-label">تأكيد كلمة المرور</label>
                <div className="relative">
                  <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type={showConfirmPassword ? 'text' : 'password'}
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className={`form-input pr-10 pl-10 ${errors.confirmPassword ? 'border-danger-500' : ''}`}
                    placeholder="أعد إدخال كلمة المرور"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="mt-1 text-sm text-danger-600 flex items-center">
                    <AlertCircle className="w-4 h-4 ml-1" />
                    {errors.confirmPassword}
                  </p>
                )}
              </div>
            )}
          </div>

          {/* الموافقة على الشروط - للتسجيل فقط */}
          {!isLogin && (
            <div>
              <label className="flex items-start">
                <input
                  type="checkbox"
                  name="agreeToTerms"
                  checked={formData.agreeToTerms}
                  onChange={handleInputChange}
                  className="mt-1 ml-2"
                />
                <span className="text-sm text-gray-600">
                  أوافق على{' '}
                  <a href="#" className="text-primary-600 hover:text-primary-700 font-medium">
                    الشروط والأحكام
                  </a>
                  {' '}و{' '}
                  <a href="#" className="text-primary-600 hover:text-primary-700 font-medium">
                    سياسة الخصوصية
                  </a>
                </span>
              </label>
              {errors.agreeToTerms && (
                <p className="mt-1 text-sm text-danger-600 flex items-center">
                  <AlertCircle className="w-4 h-4 ml-1" />
                  {errors.agreeToTerms}
                </p>
              )}
            </div>
          )}

          {/* تذكرني ونسيت كلمة المرور - لتسجيل الدخول فقط */}
          {isLogin && (
            <div className="flex items-center justify-between">
              <label className="flex items-center">
                <input type="checkbox" className="ml-2" />
                <span className="text-sm text-gray-600">تذكرني</span>
              </label>
              <a href="#" className="text-sm text-primary-600 hover:text-primary-700 font-medium">
                نسيت كلمة المرور؟
              </a>
            </div>
          )}

          {/* زر الإرسال */}
          <button
            type="submit"
            disabled={isLoading}
            className={`w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold py-4 px-6 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl text-lg ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
                جاري المعالجة...
              </div>
            ) : (
              <div className="flex items-center justify-center">
                {isLogin ? 'تسجيل الدخول' : 'إنشاء الحساب'}
                <ArrowRight className="w-5 h-5 mr-2" />
              </div>
            )}
          </button>

          {/* التبديل بين تسجيل الدخول والتسجيل */}
          <div className="text-center">
            <p className="text-sm text-gray-600">
              {isLogin ? 'ليس لديك حساب؟' : 'لديك حساب بالفعل؟'}
              {' '}
              <button
                type="button"
                onClick={() => {
                  setIsLogin(!isLogin);
                  setErrors({});
                  setFormData({
                    email: '',
                    password: '',
                    confirmPassword: '',
                    fullName: '',
                    phone: '',
                    agreeToTerms: false
                  });
                }}
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                {isLogin ? 'إنشاء حساب جديد' : 'تسجيل الدخول'}
              </button>
            </p>
          </div>
        </form>

        {/* ميزات الأمان */}
        <div className="mt-8 bg-gray-100 rounded-lg p-4">
          <div className="flex items-center mb-3">
            <Shield className="w-5 h-5 text-primary-600 ml-2" />
            <span className="font-medium text-gray-900">حماية متقدمة</span>
          </div>
          <ul className="space-y-2 text-sm text-gray-600">
            <li className="flex items-center">
              <CheckCircle className="w-4 h-4 text-success-500 ml-2" />
              تشفير SSL 256-bit
            </li>
            <li className="flex items-center">
              <CheckCircle className="w-4 h-4 text-success-500 ml-2" />
              مصادقة ثنائية العامل
            </li>
            <li className="flex items-center">
              <CheckCircle className="w-4 h-4 text-success-500 ml-2" />
              حماية من الاحتيال
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}