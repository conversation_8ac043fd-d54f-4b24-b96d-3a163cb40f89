// أنواع البيانات للمشروع

export interface User {
  id: string;
  fullName: string;
  email: string;
  phone: string;
  rating: number;
  totalTrades: number;
  completionRate: number;
  isVerified: boolean;
  isOnline: boolean;
  country: string;
  joinedAt: Date;
  lastSeen: Date;
}

export interface Offer {
  id: string;
  sellerId: string;
  seller: User;
  amount: number;
  minAmount: number;
  maxAmount: number;
  price: number;
  currency: string;
  paymentMethods: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Trade {
  id: string;
  offerId: string;
  sellerId: string;
  buyerId: string;
  amount: number;
  price: number;
  totalAmount: number;
  currency: string;
  paymentMethod: string;
  status: TradeStatus;
  createdAt: Date;
  updatedAt: Date;
  paymentSentAt?: Date;
  completedAt?: Date;
  cancelledAt?: Date;
  disputedAt?: Date;
  messages: TradeMessage[];
}

export enum TradeStatus {
  CREATED = 'CREATED',
  BUYER_JOINED = 'BUYER_JOINED',
  PAYMENT_SENT = 'PAYMENT_SENT',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  DISPUTED = 'DISPUTED'
}

export interface TradeMessage {
  id: string;
  tradeId: string;
  senderId: string;
  message: string;
  type: 'text' | 'system' | 'payment_proof';
  createdAt: Date;
}

export interface PaymentMethod {
  id: string;
  name: string;
  nameAr: string;
  icon: string;
  isActive: boolean;
}

export interface Country {
  code: string;
  name: string;
  nameAr: string;
  currency: string;
  flag: string;
}

export interface PlatformStats {
  totalTrades: number;
  totalUsers: number;
  totalVolume: number;
  averageRating: number;
  activeOffers: number;
  onlineUsers: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface FilterOptions {
  country?: string;
  paymentMethod?: string;
  currency?: string;
  minAmount?: number;
  maxAmount?: number;
  sortBy?: 'price' | 'rating' | 'trades' | 'created';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface NotificationSettings {
  email: boolean;
  sms: boolean;
  push: boolean;
  tradeUpdates: boolean;
  marketingEmails: boolean;
}

export interface UserSettings {
  language: 'ar' | 'en';
  currency: string;
  timezone: string;
  notifications: NotificationSettings;
  twoFactorEnabled: boolean;
}

export interface SecurityLog {
  id: string;
  userId: string;
  action: string;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
}

export interface Dispute {
  id: string;
  tradeId: string;
  reporterId: string;
  reason: string;
  description: string;
  status: 'open' | 'investigating' | 'resolved';
  resolution?: string;
  resolvedBy?: string;
  createdAt: Date;
  resolvedAt?: Date;
}

export interface Review {
  id: string;
  tradeId: string;
  reviewerId: string;
  revieweeId: string;
  rating: number;
  comment: string;
  createdAt: Date;
}

// أنواع النماذج
export interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

export interface RegisterFormData {
  fullName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  agreeToTerms: boolean;
}

export interface CreateOfferFormData {
  amount: number;
  minAmount: number;
  maxAmount: number;
  price: number;
  currency: string;
  paymentMethods: string[];
  terms?: string;
}

export interface TradeFormData {
  amount: number;
  paymentMethod: string;
  message?: string;
}

// أنواع الأحداث
export interface TradeEvent {
  type: 'trade_created' | 'trade_updated' | 'message_sent' | 'payment_confirmed';
  tradeId: string;
  data: any;
  timestamp: Date;
}

// أنواع الأخطاء
export interface ValidationError {
  field: string;
  message: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
}