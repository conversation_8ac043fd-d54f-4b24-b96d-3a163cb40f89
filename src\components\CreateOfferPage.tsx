'use client';

import { useState } from 'react';
import {
  DollarSign,
  CreditCard,
  AlertTriangle,
  Info,
  CheckCircle,
  ArrowRight,
  Calculator,
  Shield,
  Clock,
  Users
} from 'lucide-react';

const paymentMethods = [
  { id: 'bank_transfer', name: 'تحويل بنكي', nameEn: 'Bank Transfer' },
  { id: 'quick_transfer', name: 'حوالة سريعة', nameEn: 'Quick Transfer' },
  { id: 'instant_transfer', name: 'تحويل فوري', nameEn: 'Instant Transfer' },
  { id: 'cash', name: 'نقداً', nameEn: 'Cash' },
  { id: 'mobile_wallet', name: 'محفظة إلكترونية', nameEn: 'Mobile Wallet' }
];

const currencies = [
  { code: 'SAR', name: 'ريال سعودي', symbol: 'ر.س' },
  { code: 'AED', name: 'درهم إماراتي', symbol: 'د.إ' },
  { code: 'KWD', name: 'دينار كويتي', symbol: 'د.ك' },
  { code: 'QAR', name: 'ريال قطري', symbol: 'ر.ق' }
];

export default function CreateOfferPage() {
  const [offerType, setOfferType] = useState<'buy' | 'sell'>('sell');
  const [formData, setFormData] = useState({
    amount: '',
    minAmount: '',
    maxAmount: '',
    price: '',
    currency: 'SAR',
    paymentMethods: [] as string[],
    terms: '',
    autoReply: ''
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isLoading, setIsLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // إزالة الخطأ عند التعديل
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handlePaymentMethodChange = (methodId: string) => {
    setFormData(prev => ({
      ...prev,
      paymentMethods: prev.paymentMethods.includes(methodId)
        ? prev.paymentMethods.filter(id => id !== methodId)
        : [...prev.paymentMethods, methodId]
    }));
  };

  const validateStep = (step: number) => {
    const newErrors: {[key: string]: string} = {};

    if (step === 1) {
      if (!formData.amount) {
        newErrors.amount = 'الكمية الإجمالية مطلوبة';
      } else if (parseFloat(formData.amount) <= 0) {
        newErrors.amount = 'الكمية يجب أن تكون أكبر من صفر';
      }

      if (!formData.minAmount) {
        newErrors.minAmount = 'الحد الأدنى مطلوب';
      } else if (parseFloat(formData.minAmount) <= 0) {
        newErrors.minAmount = 'الحد الأدنى يجب أن يكون أكبر من صفر';
      }

      if (!formData.maxAmount) {
        newErrors.maxAmount = 'الحد الأقصى مطلوب';
      } else if (parseFloat(formData.maxAmount) <= 0) {
        newErrors.maxAmount = 'الحد الأقصى يجب أن يكون أكبر من صفر';
      }

      if (formData.minAmount && formData.maxAmount && parseFloat(formData.minAmount) > parseFloat(formData.maxAmount)) {
        newErrors.maxAmount = 'الحد الأقصى يجب أن يكون أكبر من الحد الأدنى';
      }

      if (formData.amount && formData.maxAmount && parseFloat(formData.maxAmount) > parseFloat(formData.amount)) {
        newErrors.maxAmount = 'الحد الأقصى لا يمكن أن يكون أكبر من الكمية الإجمالية';
      }
    }

    if (step === 2) {
      if (!formData.price) {
        newErrors.price = 'السعر مطلوب';
      } else if (parseFloat(formData.price) <= 0) {
        newErrors.price = 'السعر يجب أن يكون أكبر من صفر';
      }

      if (formData.paymentMethods.length === 0) {
        newErrors.paymentMethods = 'يجب اختيار طريقة دفع واحدة على الأقل';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) {
      return;
    }

    setIsLoading(true);

    try {
      // محاكاة إنشاء العرض
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert('تم إنشاء العرض بنجاح!');
      // إعادة توجيه إلى لوحة التحكم
    } catch (error) {
      alert('حدث خطأ، يرجى المحاولة مرة أخرى');
    } finally {
      setIsLoading(false);
    }
  };

  const calculateTotal = () => {
    const amount = parseFloat(formData.amount) || 0;
    const price = parseFloat(formData.price) || 0;
    return (amount * price).toFixed(2);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container-custom max-w-4xl">
        {/* العنوان */}
        <div className="text-center mb-8">
          <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            إنشاء عرض جديد
          </h1>
          <p className="text-xl text-gray-600">
            أنشئ عرضك للبيع أو الشراء واربح من تداول USDT
          </p>
        </div>

        {/* مؤشر التقدم */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4 space-x-reverse">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${
                  step <= currentStep 
                    ? 'bg-primary-600 text-white' 
                    : 'bg-gray-200 text-gray-500'
                }`}>
                  {step < currentStep ? <CheckCircle className="w-5 h-5" /> : step}
                </div>
                {step < 3 && (
                  <div className={`w-16 h-1 mx-2 ${
                    step < currentStep ? 'bg-primary-600' : 'bg-gray-200'
                  }`}></div>
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-center mt-4">
            <div className="text-sm text-gray-600">
              {currentStep === 1 && 'تحديد الكمية والحدود'}
              {currentStep === 2 && 'تحديد السعر وطرق الدفع'}
              {currentStep === 3 && 'الشروط والمراجعة النهائية'}
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* النموذج الرئيسي */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              {/* اختيار نوع العرض */}
              <div className="mb-6">
                <label className="form-label">نوع العرض</label>
                <div className="grid grid-cols-2 gap-4">
                  <button
                    type="button"
                    onClick={() => setOfferType('sell')}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      offerType === 'sell'
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="text-center">
                      <DollarSign className="w-8 h-8 mx-auto mb-2" />
                      <div className="font-semibold">بيع USDT</div>
                      <div className="text-sm text-gray-600">أبيع عملة USDT مقابل عملة محلية</div>
                    </div>
                  </button>
                  <button
                    type="button"
                    onClick={() => setOfferType('buy')}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      offerType === 'buy'
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="text-center">
                      <CreditCard className="w-8 h-8 mx-auto mb-2" />
                      <div className="font-semibold">شراء USDT</div>
                      <div className="text-sm text-gray-600">أشتري عملة USDT بعملة محلية</div>
                    </div>
                  </button>
                </div>
              </div>

              {/* الخطوة الأولى: الكمية والحدود */}
              {currentStep === 1 && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    تحديد الكمية والحدود
                  </h3>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="form-label">الكمية الإجمالية (USDT)</label>
                      <input
                        type="number"
                        name="amount"
                        value={formData.amount}
                        onChange={handleInputChange}
                        className={`form-input ${errors.amount ? 'border-danger-500' : ''}`}
                        placeholder="1000"
                      />
                      {errors.amount && (
                        <p className="mt-1 text-sm text-danger-600">{errors.amount}</p>
                      )}
                    </div>

                    <div>
                      <label className="form-label">العملة المحلية</label>
                      <select
                        name="currency"
                        value={formData.currency}
                        onChange={handleInputChange}
                        className="form-input"
                      >
                        {currencies.map(currency => (
                          <option key={currency.code} value={currency.code}>
                            {currency.name} ({currency.symbol})
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="form-label">الحد الأدنى للصفقة (USDT)</label>
                      <input
                        type="number"
                        name="minAmount"
                        value={formData.minAmount}
                        onChange={handleInputChange}
                        className={`form-input ${errors.minAmount ? 'border-danger-500' : ''}`}
                        placeholder="100"
                      />
                      {errors.minAmount && (
                        <p className="mt-1 text-sm text-danger-600">{errors.minAmount}</p>
                      )}
                    </div>

                    <div>
                      <label className="form-label">الحد الأقصى للصفقة (USDT)</label>
                      <input
                        type="number"
                        name="maxAmount"
                        value={formData.maxAmount}
                        onChange={handleInputChange}
                        className={`form-input ${errors.maxAmount ? 'border-danger-500' : ''}`}
                        placeholder="1000"
                      />
                      {errors.maxAmount && (
                        <p className="mt-1 text-sm text-danger-600">{errors.maxAmount}</p>
                      )}
                    </div>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <Info className="w-5 h-5 text-blue-600 mt-0.5 ml-2" />
                      <div className="text-sm text-blue-800">
                        <p className="font-medium mb-1">نصائح مهمة:</p>
                        <ul className="space-y-1">
                          <li>• تأكد من أن لديك الكمية المطلوبة في محفظتك</li>
                          <li>• الحد الأدنى يساعد في تقليل الصفقات الصغيرة</li>
                          <li>• الحد الأقصى يحدد أكبر صفقة يمكن تنفيذها</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <button
                      onClick={handleNext}
                      className="btn btn-primary"
                    >
                      التالي
                      <ArrowRight className="w-4 h-4 mr-2" />
                    </button>
                  </div>
                </div>
              )}

              {/* الخطوة الثانية: السعر وطرق الدفع */}
              {currentStep === 2 && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    تحديد السعر وطرق الدفع
                  </h3>

                  <div>
                    <label className="form-label">
                      السعر لكل USDT ({currencies.find(c => c.code === formData.currency)?.symbol})
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        step="0.01"
                        name="price"
                        value={formData.price}
                        onChange={handleInputChange}
                        className={`form-input pr-12 ${errors.price ? 'border-danger-500' : ''}`}
                        placeholder="3.67"
                      />
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        {currencies.find(c => c.code === formData.currency)?.symbol}
                      </div>
                    </div>
                    {errors.price && (
                      <p className="mt-1 text-sm text-danger-600">{errors.price}</p>
                    )}
                  </div>

                  <div>
                    <label className="form-label">طرق الدفع المقبولة</label>
                    <div className="grid md:grid-cols-2 gap-3">
                      {paymentMethods.map(method => (
                        <label
                          key={method.id}
                          className={`flex items-center p-3 rounded-lg border-2 cursor-pointer transition-all ${
                            formData.paymentMethods.includes(method.id)
                              ? 'border-primary-500 bg-primary-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <input
                            type="checkbox"
                            checked={formData.paymentMethods.includes(method.id)}
                            onChange={() => handlePaymentMethodChange(method.id)}
                            className="ml-3"
                          />
                          <div>
                            <div className="font-medium text-gray-900">{method.name}</div>
                            <div className="text-sm text-gray-600">{method.nameEn}</div>
                          </div>
                        </label>
                      ))}
                    </div>
                    {errors.paymentMethods && (
                      <p className="mt-1 text-sm text-danger-600">{errors.paymentMethods}</p>
                    )}
                  </div>

                  <div className="flex justify-between">
                    <button
                      onClick={() => setCurrentStep(1)}
                      className="btn btn-secondary"
                    >
                      السابق
                    </button>
                    <button
                      onClick={handleNext}
                      className="btn btn-primary"
                    >
                      التالي
                      <ArrowRight className="w-4 h-4 mr-2" />
                    </button>
                  </div>
                </div>
              )}

              {/* الخطوة الثالثة: الشروط والمراجعة */}
              {currentStep === 3 && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    الشروط والمراجعة النهائية
                  </h3>

                  <div>
                    <label className="form-label">شروط العرض (اختياري)</label>
                    <textarea
                      name="terms"
                      value={formData.terms}
                      onChange={handleInputChange}
                      rows={4}
                      className="form-input"
                      placeholder="أضف أي شروط خاصة بعرضك..."
                    />
                  </div>

                  <div>
                    <label className="form-label">رسالة ترحيب تلقائية (اختياري)</label>
                    <textarea
                      name="autoReply"
                      value={formData.autoReply}
                      onChange={handleInputChange}
                      rows={3}
                      className="form-input"
                      placeholder="مرحباً! شكراً لاختيار عرضي..."
                    />
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 ml-2" />
                      <div className="text-sm text-yellow-800">
                        <p className="font-medium mb-1">تحذيرات مهمة:</p>
                        <ul className="space-y-1">
                          <li>• تأكد من صحة جميع المعلومات قبل النشر</li>
                          <li>• لا تشارك معلومات شخصية في الشروط</li>
                          <li>• التزم بقوانين المنصة وشروط الاستخدام</li>
                          <li>• كن متاحاً للرد على الاستفسارات</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between">
                    <button
                      onClick={() => setCurrentStep(2)}
                      className="btn btn-secondary"
                    >
                      السابق
                    </button>
                    <button
                      onClick={handleSubmit}
                      disabled={isLoading}
                      className="btn btn-primary"
                    >
                      {isLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                          جاري النشر...
                        </>
                      ) : (
                        <>
                          نشر العرض
                          <CheckCircle className="w-4 h-4 mr-2" />
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* الشريط الجانبي */}
          <div className="space-y-6">
            {/* معاينة العرض */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">معاينة العرض</h3>
              
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">النوع:</span>
                  <span className="font-medium">
                    {offerType === 'sell' ? 'بيع USDT' : 'شراء USDT'}
                  </span>
                </div>
                
                {formData.amount && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">الكمية:</span>
                    <span className="font-medium">{formData.amount} USDT</span>
                  </div>
                )}
                
                {formData.price && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">السعر:</span>
                    <span className="font-medium">
                      {formData.price} {currencies.find(c => c.code === formData.currency)?.symbol}
                    </span>
                  </div>
                )}
                
                {formData.amount && formData.price && (
                  <div className="flex justify-between border-t pt-4">
                    <span className="text-gray-600">القيمة الإجمالية:</span>
                    <span className="font-bold text-primary-600">
                      {calculateTotal()} {currencies.find(c => c.code === formData.currency)?.symbol}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* نصائح */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">نصائح للنجاح</h3>
              
              <div className="space-y-4">
                <div className="flex items-start">
                  <Calculator className="w-5 h-5 text-primary-600 mt-0.5 ml-2" />
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">سعر تنافسي</p>
                    <p className="text-gray-600">راجع أسعار السوق لتحديد سعر مناسب</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <Clock className="w-5 h-5 text-primary-600 mt-0.5 ml-2" />
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">استجابة سريعة</p>
                    <p className="text-gray-600">رد على الرسائل خلال 5 دقائق</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <Shield className="w-5 h-5 text-primary-600 mt-0.5 ml-2" />
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">أمان عالي</p>
                    <p className="text-gray-600">تحقق من هوية المشتري قبل التحويل</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <Users className="w-5 h-5 text-primary-600 mt-0.5 ml-2" />
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">خدمة ممتازة</p>
                    <p className="text-gray-600">كن مهذباً ومساعداً مع العملاء</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
