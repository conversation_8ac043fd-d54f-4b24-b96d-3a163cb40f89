// ثوابت المشروع

export const APP_CONFIG = {
  name: 'إيكاروس P2P',
  nameEn: 'IKAROS P2P',
  description: 'منصة آمنة وموثوقة لتبادل عملة USDT بين الأفراد مباشرة',
  version: '1.0.0',
  author: 'فريق إيكاروس',
  website: 'https://ikaros-p2p.com',
  supportEmail: '<EMAIL>',
  supportPhone: '+966 50 123 4567'
};

export const SUPPORTED_COUNTRIES = [
  { code: 'SA', name: 'Saudi Arabia', nameAr: 'السعودية', currency: 'SAR', flag: '🇸🇦' },
  { code: 'AE', name: 'United Arab Emirates', nameAr: 'الإمارات', currency: 'AED', flag: '🇦🇪' },
  { code: 'KW', name: 'Kuwait', nameAr: 'الكويت', currency: 'KWD', flag: '🇰🇼' },
  { code: 'QA', name: 'Qatar', nameAr: 'قطر', currency: 'QAR', flag: '🇶🇦' },
  { code: 'BH', name: 'Bahrain', nameAr: 'البحرين', currency: 'BHD', flag: '🇧🇭' },
  { code: 'OM', name: 'Oman', nameAr: 'عمان', currency: 'OMR', flag: '🇴🇲' },
  { code: 'JO', name: 'Jordan', nameAr: 'الأردن', currency: 'JOD', flag: '🇯🇴' },
  { code: 'LB', name: 'Lebanon', nameAr: 'لبنان', currency: 'LBP', flag: '🇱🇧' },
  { code: 'EG', name: 'Egypt', nameAr: 'مصر', currency: 'EGP', flag: '🇪🇬' },
  { code: 'MA', name: 'Morocco', nameAr: 'المغرب', currency: 'MAD', flag: '🇲🇦' }
];

export const PAYMENT_METHODS = [
  { id: 'bank_transfer', name: 'Bank Transfer', nameAr: 'تحويل بنكي', icon: '🏦' },
  { id: 'quick_transfer', name: 'Quick Transfer', nameAr: 'حوالة سريعة', icon: '⚡' },
  { id: 'instant_transfer', name: 'Instant Transfer', nameAr: 'تحويل فوري', icon: '🚀' },
  { id: 'cash', name: 'Cash', nameAr: 'كاش', icon: '💵' },
  { id: 'mobile_wallet', name: 'Mobile Wallet', nameAr: 'محفظة إلكترونية', icon: '📱' },
  { id: 'credit_card', name: 'Credit Card', nameAr: 'بطاقة ائتمان', icon: '💳' }
];

export const TRADE_STATUS_LABELS = {
  CREATED: 'تم الإنشاء',
  BUYER_JOINED: 'انضم المشتري',
  PAYMENT_SENT: 'تم إرسال الدفعة',
  COMPLETED: 'مكتملة',
  CANCELLED: 'ملغية',
  DISPUTED: 'متنازع عليها'
};

export const TRADE_STATUS_COLORS = {
  CREATED: 'bg-blue-100 text-blue-800',
  BUYER_JOINED: 'bg-yellow-100 text-yellow-800',
  PAYMENT_SENT: 'bg-orange-100 text-orange-800',
  COMPLETED: 'bg-green-100 text-green-800',
  CANCELLED: 'bg-red-100 text-red-800',
  DISPUTED: 'bg-purple-100 text-purple-800'
};

export const CURRENCY_SYMBOLS = {
  SAR: 'ر.س',
  AED: 'د.إ',
  KWD: 'د.ك',
  QAR: 'ر.ق',
  BHD: 'د.ب',
  OMR: 'ر.ع',
  JOD: 'د.أ',
  LBP: 'ل.ل',
  EGP: 'ج.م',
  MAD: 'د.م',
  USD: '$',
  EUR: '€'
};

export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
    FORGOT_PASSWORD: '/api/auth/forgot-password',
    RESET_PASSWORD: '/api/auth/reset-password',
    VERIFY_EMAIL: '/api/auth/verify-email'
  },
  USERS: {
    PROFILE: '/api/users/profile',
    UPDATE_PROFILE: '/api/users/profile',
    SETTINGS: '/api/users/settings',
    SECURITY_LOGS: '/api/users/security-logs'
  },
  OFFERS: {
    LIST: '/api/offers',
    CREATE: '/api/offers',
    UPDATE: '/api/offers',
    DELETE: '/api/offers',
    MY_OFFERS: '/api/offers/my-offers'
  },
  TRADES: {
    LIST: '/api/trades',
    CREATE: '/api/trades',
    UPDATE: '/api/trades',
    MESSAGES: '/api/trades/messages',
    CONFIRM_PAYMENT: '/api/trades/confirm-payment',
    COMPLETE: '/api/trades/complete',
    CANCEL: '/api/trades/cancel',
    DISPUTE: '/api/trades/dispute'
  },
  ADMIN: {
    STATS: '/api/admin/stats',
    USERS: '/api/admin/users',
    TRADES: '/api/admin/trades',
    DISPUTES: '/api/admin/disputes'
  }
};

export const VALIDATION_RULES = {
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    MESSAGE: 'البريد الإلكتروني غير صحيح'
  },
  PASSWORD: {
    MIN_LENGTH: 8,
    PATTERN: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    MESSAGE: 'كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل، حرف كبير، حرف صغير، رقم، ورمز خاص'
  },
  PHONE: {
    PATTERN: /^[+]?[0-9\s\-\(\)]{10,15}$/,
    MESSAGE: 'رقم الهاتف غير صحيح'
  },
  AMOUNT: {
    MIN: 10,
    MAX: 100000,
    MESSAGE: 'المبلغ يجب أن يكون بين 10 و 100,000'
  }
};

export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100
};

export const CACHE_KEYS = {
  USER_PROFILE: 'user_profile',
  OFFERS_LIST: 'offers_list',
  TRADES_LIST: 'trades_list',
  PLATFORM_STATS: 'platform_stats'
};

export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_PREFERENCES: 'user_preferences',
  THEME: 'theme',
  LANGUAGE: 'language'
};

export const WEBSOCKET_EVENTS = {
  TRADE_UPDATED: 'trade_updated',
  MESSAGE_RECEIVED: 'message_received',
  OFFER_UPDATED: 'offer_updated',
  USER_ONLINE: 'user_online',
  USER_OFFLINE: 'user_offline'
};

export const NOTIFICATION_TYPES = {
  TRADE_CREATED: 'trade_created',
  TRADE_UPDATED: 'trade_updated',
  PAYMENT_RECEIVED: 'payment_received',
  TRADE_COMPLETED: 'trade_completed',
  TRADE_CANCELLED: 'trade_cancelled',
  TRADE_DISPUTED: 'trade_disputed',
  MESSAGE_RECEIVED: 'message_received'
};

export const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
  TRADE_NOT_FOUND: 'TRADE_NOT_FOUND',
  OFFER_NOT_AVAILABLE: 'OFFER_NOT_AVAILABLE',
  USER_NOT_VERIFIED: 'USER_NOT_VERIFIED',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED'
};

export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'تم تسجيل الدخول بنجاح',
  REGISTER_SUCCESS: 'تم إنشاء الحساب بنجاح',
  PROFILE_UPDATED: 'تم تحديث الملف الشخصي بنجاح',
  OFFER_CREATED: 'تم إنشاء العرض بنجاح',
  TRADE_CREATED: 'تم إنشاء الصفقة بنجاح',
  PAYMENT_CONFIRMED: 'تم تأكيد الدفعة بنجاح',
  TRADE_COMPLETED: 'تم إنجاز الصفقة بنجاح'
};

export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'خطأ في الاتصال، يرجى المحاولة مرة أخرى',
  UNAUTHORIZED: 'غير مخول، يرجى تسجيل الدخول',
  FORBIDDEN: 'غير مسموح بهذا الإجراء',
  NOT_FOUND: 'العنصر المطلوب غير موجود',
  VALIDATION_ERROR: 'خطأ في البيانات المدخلة',
  SERVER_ERROR: 'خطأ في الخادم، يرجى المحاولة لاحقاً'
};

export const SOCIAL_LINKS = {
  FACEBOOK: 'https://facebook.com/ikaros-p2p',
  TWITTER: 'https://twitter.com/ikaros_p2p',
  INSTAGRAM: 'https://instagram.com/ikaros_p2p',
  LINKEDIN: 'https://linkedin.com/company/ikaros-p2p',
  TELEGRAM: 'https://t.me/ikaros_p2p',
  YOUTUBE: 'https://youtube.com/c/ikaros-p2p'
};

export const LEGAL_LINKS = {
  TERMS_OF_SERVICE: '/terms',
  PRIVACY_POLICY: '/privacy',
  SECURITY_POLICY: '/security',
  DISCLAIMER: '/disclaimer',
  AML_POLICY: '/aml-policy',
  KYC_POLICY: '/kyc-policy'
};

export const FEATURE_FLAGS = {
  ENABLE_2FA: true,
  ENABLE_KYC: true,
  ENABLE_CHAT: true,
  ENABLE_NOTIFICATIONS: true,
  ENABLE_MOBILE_APP: false,
  ENABLE_ADVANCED_TRADING: false
};

export const LIMITS = {
  MAX_OFFERS_PER_USER: 10,
  MAX_ACTIVE_TRADES: 5,
  MAX_MESSAGE_LENGTH: 500,
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  MAX_IMAGES_PER_MESSAGE: 3
};

export const TIMEOUTS = {
  API_REQUEST: 30000, // 30 seconds
  WEBSOCKET_RECONNECT: 5000, // 5 seconds
  NOTIFICATION_DISPLAY: 5000, // 5 seconds
  AUTO_LOGOUT: 30 * 60 * 1000 // 30 minutes
};