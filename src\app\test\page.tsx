export default function TestPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 to-purple-700 flex items-center justify-center">
      <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-4 text-center">
          اختبار TailwindCSS
        </h1>
        <p className="text-gray-600 mb-6 text-center">
          إذا كنت ترى هذا التصميم بالألوان والخطوط الصحيحة، فإن TailwindCSS يعمل بشكل صحيح.
        </p>
        <div className="space-y-4">
          <button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold py-3 px-6 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
            زر تجريبي
          </button>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">السعر:</span>
              <span className="font-bold text-blue-600">3.67 SAR</span>
            </div>
          </div>
          <div className="flex space-x-2 space-x-reverse">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-green-600 text-sm">متصل الآن</span>
          </div>
        </div>
      </div>
    </div>
  );
}
