'use client';

import { useState, useEffect } from 'react';
import {
  Wallet,
  AlertCircle,
  CheckCircle,
  ExternalLink,
  Copy,
  LogOut
} from 'lucide-react';

interface WalletInfo {
  address: string;
  balance: string;
  network: string;
  isConnected: boolean;
}

export default function WalletConnect() {
  const [wallet, setWallet] = useState<WalletInfo | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string>('');
  const [showWalletOptions, setShowWalletOptions] = useState(false);

  // التحقق من وجود MetaMask
  const isMetaMaskInstalled = () => {
    return typeof window !== 'undefined' && typeof (window as any).ethereum !== 'undefined';
  };

  // محاكاة الاتصال بالمحفظة
  const connectWallet = async (walletType: 'metamask' | 'walletconnect' | 'trustwallet') => {
    setIsConnecting(true);
    setError('');

    try {
      // محاكاة تأخير الاتصال
      await new Promise(resolve => setTimeout(resolve, 2000));

      if (walletType === 'metamask' && !isMetaMaskInstalled()) {
        throw new Error('MetaMask غير مثبت. يرجى تثبيت MetaMask أولاً.');
      }

      // محاكاة بيانات المحفظة
      const mockWallet: WalletInfo = {
        address: '******************************************',
        balance: '1,250.50',
        network: 'BSC Mainnet',
        isConnected: true
      };

      setWallet(mockWallet);
      setShowWalletOptions(false);
      
      // حفظ حالة الاتصال في localStorage
      localStorage.setItem('wallet_connected', 'true');
      localStorage.setItem('wallet_data', JSON.stringify(mockWallet));

    } catch (err: any) {
      setError(err.message || 'فشل في الاتصال بالمحفظة');
    } finally {
      setIsConnecting(false);
    }
  };

  // قطع الاتصال
  const disconnectWallet = () => {
    setWallet(null);
    localStorage.removeItem('wallet_connected');
    localStorage.removeItem('wallet_data');
  };

  // نسخ العنوان
  const copyAddress = () => {
    if (wallet?.address) {
      navigator.clipboard.writeText(wallet.address);
      // يمكن إضافة إشعار هنا
    }
  };

  // التحقق من الاتصال المحفوظ عند التحميل
  useEffect(() => {
    const savedConnection = localStorage.getItem('wallet_connected');
    const savedWalletData = localStorage.getItem('wallet_data');
    
    if (savedConnection === 'true' && savedWalletData) {
      try {
        const walletData = JSON.parse(savedWalletData);
        setWallet(walletData);
      } catch (error) {
        localStorage.removeItem('wallet_connected');
        localStorage.removeItem('wallet_data');
      }
    }
  }, []);

  // عرض المحفظة المتصلة
  if (wallet?.isConnected) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-success-100 rounded-full flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-success-600" />
            </div>
            <div className="mr-3">
              <h3 className="font-semibold text-gray-900">محفظة متصلة</h3>
              <p className="text-sm text-gray-600">{wallet.network}</p>
            </div>
          </div>
          <button
            onClick={disconnectWallet}
            className="text-gray-400 hover:text-danger-600 transition-colors"
            title="قطع الاتصال"
          >
            <LogOut className="w-5 h-5" />
          </button>
        </div>

        <div className="space-y-3">
          <div>
            <label className="text-sm text-gray-600">عنوان المحفظة</label>
            <div className="flex items-center justify-between bg-gray-50 rounded-lg p-3 mt-1">
              <span className="text-sm font-mono text-gray-800">
                {wallet.address.slice(0, 6)}...{wallet.address.slice(-4)}
              </span>
              <button
                onClick={copyAddress}
                className="text-gray-400 hover:text-primary-600 transition-colors"
                title="نسخ العنوان"
              >
                <Copy className="w-4 h-4" />
              </button>
            </div>
          </div>

          <div>
            <label className="text-sm text-gray-600">رصيد USDT</label>
            <div className="text-2xl font-bold text-gray-900 mt-1">
              {wallet.balance} USDT
            </div>
          </div>
        </div>

        <div className="mt-4 pt-4 border-t border-gray-200">
          <button className="btn btn-primary w-full">
            عرض في المستكشف
            <ExternalLink className="w-4 h-4 mr-2" />
          </button>
        </div>
      </div>
    );
  }

  // عرض خيارات الاتصال
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Wallet className="w-8 h-8 text-primary-600" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-2">
          اربط محفظتك الرقمية
        </h3>
        <p className="text-gray-600">
          اربط محفظتك لبدء التداول الآمن على منصة إيكاروس P2P
        </p>
      </div>

      {error && (
        <div className="bg-danger-50 border border-danger-200 rounded-lg p-4 mb-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-danger-600 ml-2" />
            <span className="text-danger-800 text-sm">{error}</span>
          </div>
        </div>
      )}

      {!showWalletOptions ? (
        <button
          onClick={() => setShowWalletOptions(true)}
          className="btn btn-primary w-full text-lg py-4"
        >
          اختر محفظة
        </button>
      ) : (
        <div className="space-y-3">
          {/* MetaMask */}
          <button
            onClick={() => connectWallet('metamask')}
            disabled={isConnecting}
            className="w-full flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors disabled:opacity-50"
          >
            <div className="flex items-center">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <span className="text-orange-600 font-bold">M</span>
              </div>
              <div className="mr-3 text-right">
                <div className="font-semibold text-gray-900">MetaMask</div>
                <div className="text-sm text-gray-600">
                  {isMetaMaskInstalled() ? 'متاح' : 'غير مثبت'}
                </div>
              </div>
            </div>
            {isConnecting ? (
              <div className="loading-spinner"></div>
            ) : (
              <ExternalLink className="w-5 h-5 text-gray-400" />
            )}
          </button>

          {/* WalletConnect */}
          <button
            onClick={() => connectWallet('walletconnect')}
            disabled={isConnecting}
            className="w-full flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors disabled:opacity-50"
          >
            <div className="flex items-center">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 font-bold">W</span>
              </div>
              <div className="mr-3 text-right">
                <div className="font-semibold text-gray-900">WalletConnect</div>
                <div className="text-sm text-gray-600">اربط أي محفظة</div>
              </div>
            </div>
            {isConnecting ? (
              <div className="loading-spinner"></div>
            ) : (
              <ExternalLink className="w-5 h-5 text-gray-400" />
            )}
          </button>

          {/* Trust Wallet */}
          <button
            onClick={() => connectWallet('trustwallet')}
            disabled={isConnecting}
            className="w-full flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors disabled:opacity-50"
          >
            <div className="flex items-center">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 font-bold">T</span>
              </div>
              <div className="mr-3 text-right">
                <div className="font-semibold text-gray-900">Trust Wallet</div>
                <div className="text-sm text-gray-600">محفظة الجوال</div>
              </div>
            </div>
            {isConnecting ? (
              <div className="loading-spinner"></div>
            ) : (
              <ExternalLink className="w-5 h-5 text-gray-400" />
            )}
          </button>

          <button
            onClick={() => setShowWalletOptions(false)}
            className="btn btn-secondary w-full mt-4"
          >
            إلغاء
          </button>
        </div>
      )}

      {/* معلومات إضافية */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="text-center">
          <p className="text-sm text-gray-600 mb-2">
            لا تملك محفظة رقمية؟
          </p>
          <a
            href="https://metamask.io"
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            تعلم كيفية إنشاء محفظة
            <ExternalLink className="w-3 h-3 mr-1 inline" />
          </a>
        </div>
      </div>
    </div>
  );
}