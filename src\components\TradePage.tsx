'use client';

import { useState, useEffect, useRef } from 'react';
import {
  MessageCircle,
  Send,
  Upload,
  Download,
  Clock,
  Shield,
  AlertTriangle,
  CheckCircle,
  X,
  User,
  Star,
  Copy,
  ExternalLink,
  FileText,
  Camera
} from 'lucide-react';

interface TradePageProps {
  tradeId: string;
}

// بيانات وهمية للصفقة
const mockTrade = {
  id: "T001",
  amount: 1000,
  price: 3.67,
  totalAmount: 3670,
  currency: "SAR",
  paymentMethod: "تحويل بنكي",
  status: "payment_sent",
  seller: {
    id: "U001",
    name: "فاطمة علي السعيد",
    rating: 4.8,
    trades: 89,
    isOnline: true
  },
  buyer: {
    id: "U002", 
    name: "أحمد محمد الأحمد",
    rating: 4.9,
    trades: 156,
    isOnline: true
  },
  createdAt: "2024-01-15T10:30:00Z",
  timeLimit: 30, // دقيقة
  bankDetails: {
    bankName: "البنك الأهلي السعودي",
    accountNumber: "**********",
    accountName: "فاطمة علي السعيد",
    iban: "SA********************12"
  }
};

const mockMessages = [
  {
    id: "M001",
    senderId: "U001",
    message: "مرحباً! شكراً لاختيار عرضي. يرجى التحويل إلى الحساب المذكور أعلاه.",
    type: "text",
    timestamp: "2024-01-15T10:31:00Z"
  },
  {
    id: "M002",
    senderId: "U002",
    message: "مرحباً، سأقوم بالتحويل الآن",
    type: "text",
    timestamp: "2024-01-15T10:32:00Z"
  },
  {
    id: "M003",
    senderId: "U002",
    message: "تم التحويل، إليك إثبات الدفع",
    type: "payment_proof",
    timestamp: "2024-01-15T10:35:00Z",
    attachment: "payment_proof.jpg"
  }
];

export default function TradePage({ tradeId }: TradePageProps) {
  const [messages, setMessages] = useState(mockMessages);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(25 * 60); // 25 دقيقة بالثواني
  const [showPaymentProof, setShowPaymentProof] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const currentUserId = "U002"; // المستخدم الحالي

  // تحديث العد التنازلي
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 0) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // التمرير إلى آخر رسالة
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    const message = {
      id: `M${Date.now()}`,
      senderId: currentUserId,
      message: newMessage,
      type: "text" as const,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const message = {
        id: `M${Date.now()}`,
        senderId: currentUserId,
        message: "تم رفع إثبات الدفع",
        type: "payment_proof" as const,
        timestamp: new Date().toISOString(),
        attachment: file.name
      };

      setMessages(prev => [...prev, message]);
    }
  };

  const handleConfirmPayment = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert('تم تأكيد استلام الدفع بنجاح!');
    } catch (error) {
      alert('حدث خطأ، يرجى المحاولة مرة أخرى');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelTrade = async () => {
    if (confirm('هل أنت متأكد من إلغاء هذه الصفقة؟')) {
      setIsLoading(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 2000));
        alert('تم إلغاء الصفقة');
      } catch (error) {
        alert('حدث خطأ، يرجى المحاولة مرة أخرى');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert('تم النسخ إلى الحافظة');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container-custom max-w-6xl">
        {/* معلومات الصفقة */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                صفقة #{mockTrade.id}
              </h1>
              <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600">
                <span>شراء {mockTrade.amount} USDT</span>
                <span>•</span>
                <span>بسعر {mockTrade.price} {mockTrade.currency}</span>
                <span>•</span>
                <span>{mockTrade.paymentMethod}</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-4 space-x-reverse mt-4 lg:mt-0">
              <div className="text-center">
                <div className="text-sm text-gray-600">الوقت المتبقي</div>
                <div className={`text-xl font-bold ${timeRemaining < 300 ? 'text-danger-600' : 'text-primary-600'}`}>
                  {formatTime(timeRemaining)}
                </div>
              </div>
              <div className={`px-4 py-2 rounded-full text-sm font-medium ${
                mockTrade.status === 'payment_sent' 
                  ? 'bg-warning-100 text-warning-800'
                  : 'bg-success-100 text-success-800'
              }`}>
                {mockTrade.status === 'payment_sent' ? 'في انتظار التأكيد' : 'مكتملة'}
              </div>
            </div>
          </div>

          {/* تفاصيل الصفقة */}
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-3">تفاصيل الصفقة</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">الكمية:</span>
                  <span className="font-medium">{mockTrade.amount} USDT</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">السعر:</span>
                  <span className="font-medium">{mockTrade.price} {mockTrade.currency}</span>
                </div>
                <div className="flex justify-between border-t pt-2">
                  <span className="text-gray-600">المبلغ الإجمالي:</span>
                  <span className="font-bold text-primary-600">{mockTrade.totalAmount} {mockTrade.currency}</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-3">البائع</h3>
              <div className="flex items-center mb-2">
                <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center ml-3">
                  <User className="w-5 h-5 text-primary-600" />
                </div>
                <div>
                  <div className="font-medium text-gray-900">{mockTrade.seller.name}</div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Star className="w-3 h-3 text-yellow-400 fill-current ml-1" />
                    {mockTrade.seller.rating} ({mockTrade.seller.trades} صفقة)
                  </div>
                </div>
              </div>
              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                mockTrade.seller.isOnline ? 'bg-success-100 text-success-600' : 'bg-gray-100 text-gray-600'
              }`}>
                <div className={`w-2 h-2 rounded-full ml-1 ${
                  mockTrade.seller.isOnline ? 'bg-success-500' : 'bg-gray-400'
                }`}></div>
                {mockTrade.seller.isOnline ? 'متصل الآن' : 'غير متصل'}
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-3">معلومات البنك</h3>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="text-gray-600">البنك:</span>
                  <div className="font-medium">{mockTrade.bankDetails.bankName}</div>
                </div>
                <div>
                  <span className="text-gray-600">رقم الحساب:</span>
                  <div className="flex items-center">
                    <span className="font-medium ml-2">{mockTrade.bankDetails.accountNumber}</span>
                    <button
                      onClick={() => copyToClipboard(mockTrade.bankDetails.accountNumber)}
                      className="text-primary-600 hover:text-primary-700"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                <div>
                  <span className="text-gray-600">اسم الحساب:</span>
                  <div className="font-medium">{mockTrade.bankDetails.accountName}</div>
                </div>
                <div>
                  <span className="text-gray-600">IBAN:</span>
                  <div className="flex items-center">
                    <span className="font-medium ml-2 text-xs">{mockTrade.bankDetails.iban}</span>
                    <button
                      onClick={() => copyToClipboard(mockTrade.bankDetails.iban)}
                      className="text-primary-600 hover:text-primary-700"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* الدردشة */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 h-96 flex flex-col">
              <div className="p-4 border-b border-gray-200">
                <h3 className="font-semibold text-gray-900 flex items-center">
                  <MessageCircle className="w-5 h-5 ml-2" />
                  الدردشة
                </h3>
              </div>

              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.senderId === currentUserId ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      message.senderId === currentUserId
                        ? 'bg-primary-600 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}>
                      {message.type === 'payment_proof' ? (
                        <div className="flex items-center">
                          <FileText className="w-4 h-4 ml-2" />
                          <span className="text-sm">{message.message}</span>
                        </div>
                      ) : (
                        <p className="text-sm">{message.message}</p>
                      )}
                      <p className={`text-xs mt-1 ${
                        message.senderId === currentUserId ? 'text-blue-100' : 'text-gray-500'
                      }`}>
                        {new Date(message.timestamp).toLocaleTimeString('ar-SA', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>

              <div className="p-4 border-t border-gray-200">
                <div className="flex space-x-2 space-x-reverse">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    placeholder="اكتب رسالتك..."
                    className="flex-1 form-input"
                  />
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="btn btn-secondary p-2"
                  >
                    <Upload className="w-4 h-4" />
                  </button>
                  <button
                    onClick={handleSendMessage}
                    className="btn btn-primary p-2"
                  >
                    <Send className="w-4 h-4" />
                  </button>
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </div>
            </div>
          </div>

          {/* الإجراءات */}
          <div className="space-y-6">
            {/* حالة الصفقة */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="font-semibold text-gray-900 mb-4">حالة الصفقة</h3>
              
              <div className="space-y-4">
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-success-500 ml-3" />
                  <span className="text-sm text-gray-600">تم إنشاء الصفقة</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-success-500 ml-3" />
                  <span className="text-sm text-gray-600">تم إرسال الدفع</span>
                </div>
                <div className="flex items-center">
                  <Clock className="w-5 h-5 text-warning-500 ml-3" />
                  <span className="text-sm text-gray-600">في انتظار تأكيد البائع</span>
                </div>
                <div className="flex items-center">
                  <div className="w-5 h-5 border-2 border-gray-300 rounded-full ml-3"></div>
                  <span className="text-sm text-gray-400">تحرير العملة</span>
                </div>
              </div>
            </div>

            {/* الإجراءات */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="font-semibold text-gray-900 mb-4">الإجراءات</h3>
              
              <div className="space-y-3">
                {currentUserId === mockTrade.seller.id ? (
                  <button
                    onClick={handleConfirmPayment}
                    disabled={isLoading}
                    className="btn btn-success w-full"
                  >
                    {isLoading ? 'جاري المعالجة...' : 'تأكيد استلام الدفع'}
                  </button>
                ) : (
                  <button
                    onClick={() => setShowPaymentProof(true)}
                    className="btn btn-primary w-full"
                  >
                    <Upload className="w-4 h-4 ml-2" />
                    رفع إثبات الدفع
                  </button>
                )}
                
                <button
                  onClick={handleCancelTrade}
                  disabled={isLoading}
                  className="btn btn-danger w-full"
                >
                  <X className="w-4 h-4 ml-2" />
                  إلغاء الصفقة
                </button>
                
                <button className="btn btn-secondary w-full">
                  <AlertTriangle className="w-4 h-4 ml-2" />
                  الإبلاغ عن مشكلة
                </button>
              </div>
            </div>

            {/* تعليمات */}
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
              <h3 className="font-semibold text-blue-900 mb-3 flex items-center">
                <Shield className="w-5 h-5 ml-2" />
                تعليمات مهمة
              </h3>
              <ul className="text-sm text-blue-800 space-y-2">
                <li>• تأكد من التحويل للحساب الصحيح</li>
                <li>• احتفظ بإثبات التحويل</li>
                <li>• لا تحرر العملة قبل تأكيد الدفع</li>
                <li>• تواصل مع الدعم عند أي مشكلة</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
