'use client';

import { useState } from 'react';
import {
  BarChart3,
  Users,
  DollarSign,
  AlertTriangle,
  Settings,
  Shield,
  Activity,
  TrendingUp,
  Clock,
  Eye,
  Ban,
  CheckCircle,
  X,
  Search,
  Filter,
  Download
} from 'lucide-react';

// بيانات وهمية للإحصائيات
const platformStats = {
  totalUsers: 8932,
  activeUsers: 2847,
  totalTrades: 12547,
  activeTrades: 156,
  totalVolume: 28473920,
  monthlyVolume: 5847392,
  pendingDisputes: 3,
  resolvedDisputes: 47,
  platformFees: 284739,
  monthlyFees: 58473
};

// بيانات وهمية للمستخدمين
const mockUsers = [
  {
    id: "U001",
    name: "أحمد محمد الأحمد",
    email: "<EMAIL>",
    rating: 4.8,
    trades: 156,
    volume: 284739,
    status: "active",
    joinedAt: "2023-01-15",
    lastSeen: "2024-01-15T10:30:00Z",
    isVerified: true
  },
  {
    id: "U002",
    name: "فاطمة علي السعيد",
    email: "<EMAIL>",
    rating: 4.9,
    trades: 89,
    volume: 156847,
    status: "active",
    joinedAt: "2023-03-20",
    lastSeen: "2024-01-15T09:15:00Z",
    isVerified: true
  },
  {
    id: "U003",
    name: "محمد السعيد",
    email: "<EMAIL>",
    rating: 3.2,
    trades: 23,
    volume: 45678,
    status: "suspended",
    joinedAt: "2023-11-10",
    lastSeen: "2024-01-10T14:20:00Z",
    isVerified: false
  }
];

// بيانات وهمية للصفقات
const mockTrades = [
  {
    id: "T001",
    buyer: "أحمد محمد",
    seller: "فاطمة علي",
    amount: 1000,
    price: 3.67,
    total: 3670,
    currency: "SAR",
    status: "completed",
    createdAt: "2024-01-15T10:30:00Z",
    completedAt: "2024-01-15T10:45:00Z"
  },
  {
    id: "T002",
    buyer: "محمد السعيد",
    seller: "نورا أحمد",
    amount: 500,
    price: 3.68,
    total: 1840,
    currency: "SAR",
    status: "disputed",
    createdAt: "2024-01-14T14:20:00Z"
  },
  {
    id: "T003",
    buyer: "خالد العتيبي",
    seller: "سارة محمد",
    amount: 750,
    price: 3.66,
    total: 2745,
    currency: "SAR",
    status: "active",
    createdAt: "2024-01-13T09:15:00Z"
  }
];

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const [users, setUsers] = useState(mockUsers);
  const [trades, setTrades] = useState(mockTrades);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed':
        return 'text-success-600 bg-success-100';
      case 'suspended':
      case 'disputed':
        return 'text-danger-600 bg-danger-100';
      case 'pending':
        return 'text-warning-600 bg-warning-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'نشط';
      case 'suspended':
        return 'موقوف';
      case 'completed':
        return 'مكتملة';
      case 'disputed':
        return 'متنازع عليها';
      case 'pending':
        return 'معلقة';
      default:
        return status;
    }
  };

  const handleUserAction = (userId: string, action: string) => {
    setUsers(prev => prev.map(user => 
      user.id === userId 
        ? { ...user, status: action === 'suspend' ? 'suspended' : 'active' }
        : user
    ));
    alert(`تم ${action === 'suspend' ? 'إيقاف' : 'تفعيل'} المستخدم بنجاح`);
  };

  const handleTradeAction = (tradeId: string, action: string) => {
    setTrades(prev => prev.map(trade => 
      trade.id === tradeId 
        ? { ...trade, status: action }
        : trade
    ));
    alert(`تم ${action === 'completed' ? 'إكمال' : 'إلغاء'} الصفقة بنجاح`);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container-custom">
        {/* العنوان */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">لوحة الإدارة</h1>
          <p className="text-gray-600">مراقبة وإدارة منصة إيكاروس P2P</p>
        </div>

        {/* الإحصائيات السريعة */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-8">
          <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المستخدمين</p>
                <p className="text-xl font-bold text-gray-900">{platformStats.totalUsers.toLocaleString()}</p>
              </div>
              <Users className="w-8 h-8 text-primary-600" />
            </div>
          </div>

          <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">المستخدمون النشطون</p>
                <p className="text-xl font-bold text-gray-900">{platformStats.activeUsers.toLocaleString()}</p>
              </div>
              <Activity className="w-8 h-8 text-success-600" />
            </div>
          </div>

          <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي الصفقات</p>
                <p className="text-xl font-bold text-gray-900">{platformStats.totalTrades.toLocaleString()}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-warning-600" />
            </div>
          </div>

          <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">الصفقات النشطة</p>
                <p className="text-xl font-bold text-gray-900">{platformStats.activeTrades}</p>
              </div>
              <Clock className="w-8 h-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">النزاعات المعلقة</p>
                <p className="text-xl font-bold text-gray-900">{platformStats.pendingDisputes}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-danger-600" />
            </div>
          </div>

          <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">العمولات الشهرية</p>
                <p className="text-xl font-bold text-gray-900">{(platformStats.monthlyFees / 1000).toFixed(0)}K</p>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
          </div>
        </div>

        {/* التبويبات */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 space-x-reverse px-6">
              {[
                { id: 'overview', label: 'نظرة عامة', icon: BarChart3 },
                { id: 'users', label: 'المستخدمون', icon: Users },
                { id: 'trades', label: 'الصفقات', icon: TrendingUp },
                { id: 'disputes', label: 'النزاعات', icon: AlertTriangle },
                { id: 'settings', label: 'الإعدادات', icon: Settings }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="w-4 h-4 ml-2" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {/* تبويب النظرة العامة */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* إحصائيات مفصلة */}
                  <div className="bg-gradient-to-br from-primary-50 to-primary-100 rounded-lg p-6">
                    <h3 className="font-semibold text-primary-900 mb-4">حجم التداول</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-primary-700">إجمالي:</span>
                        <span className="font-bold text-primary-900">
                          ${(platformStats.totalVolume / 1000000).toFixed(1)}M
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-primary-700">هذا الشهر:</span>
                        <span className="font-bold text-primary-900">
                          ${(platformStats.monthlyVolume / 1000000).toFixed(1)}M
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-success-50 to-success-100 rounded-lg p-6">
                    <h3 className="font-semibold text-success-900 mb-4">العمولات</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-success-700">إجمالي:</span>
                        <span className="font-bold text-success-900">
                          ${(platformStats.platformFees / 1000).toFixed(0)}K
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-success-700">هذا الشهر:</span>
                        <span className="font-bold text-success-900">
                          ${(platformStats.monthlyFees / 1000).toFixed(0)}K
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-warning-50 to-warning-100 rounded-lg p-6">
                    <h3 className="font-semibold text-warning-900 mb-4">النزاعات</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-warning-700">معلقة:</span>
                        <span className="font-bold text-warning-900">{platformStats.pendingDisputes}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-warning-700">محلولة:</span>
                        <span className="font-bold text-warning-900">{platformStats.resolvedDisputes}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* النشاط الأخير */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">النشاط الأخير</h3>
                  <div className="space-y-3">
                    <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                      <CheckCircle className="w-5 h-5 text-success-500 ml-3" />
                      <div>
                        <p className="font-medium text-gray-900">تم إكمال صفقة T001</p>
                        <p className="text-sm text-gray-600">منذ 5 دقائق</p>
                      </div>
                    </div>
                    <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                      <Users className="w-5 h-5 text-primary-500 ml-3" />
                      <div>
                        <p className="font-medium text-gray-900">انضم مستخدم جديد: سارة أحمد</p>
                        <p className="text-sm text-gray-600">منذ 15 دقيقة</p>
                      </div>
                    </div>
                    <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                      <AlertTriangle className="w-5 h-5 text-warning-500 ml-3" />
                      <div>
                        <p className="font-medium text-gray-900">تم الإبلاغ عن نزاع في صفقة T002</p>
                        <p className="text-sm text-gray-600">منذ 30 دقيقة</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* تبويب المستخدمون */}
            {activeTab === 'users' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">إدارة المستخدمين</h3>
                  <div className="flex space-x-3 space-x-reverse">
                    <div className="input-with-icon w-64">
                      <Search className="input-icon-right w-4 h-4" />
                      <input
                        type="text"
                        placeholder="البحث عن مستخدم..."
                        className="form-input text-base-ar"
                      />
                    </div>
                    <button className="btn btn-secondary btn-sm">
                      <Filter className="w-4 h-4 ml-2" />
                      فلترة
                    </button>
                    <button className="btn btn-secondary btn-sm">
                      <Download className="w-4 h-4 ml-2" />
                      تصدير
                    </button>
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-right py-3 px-4 font-medium text-gray-900">المستخدم</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">التقييم</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">الصفقات</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">الحجم</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">الحالة</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {users.map((user) => (
                        <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-3 px-4">
                            <div className="flex items-center">
                              <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center ml-3">
                                <span className="text-primary-700 font-medium text-sm">
                                  {user.name.charAt(0)}
                                </span>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900 flex items-center">
                                  {user.name}
                                  {user.isVerified && (
                                    <Shield className="w-4 h-4 text-success-500 mr-1" />
                                  )}
                                </div>
                                <div className="text-sm text-gray-600">{user.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="py-3 px-4 text-sm text-gray-600">{user.rating}</td>
                          <td className="py-3 px-4 text-sm text-gray-600">{user.trades}</td>
                          <td className="py-3 px-4 text-sm text-gray-600">
                            ${(user.volume / 1000).toFixed(0)}K
                          </td>
                          <td className="py-3 px-4">
                            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
                              {getStatusText(user.status)}
                            </span>
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex space-x-2 space-x-reverse">
                              <button
                                onClick={() => alert(`عرض تفاصيل ${user.name}`)}
                                className="text-primary-600 hover:text-primary-700"
                              >
                                <Eye className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleUserAction(user.id, user.status === 'active' ? 'suspend' : 'activate')}
                                className={`${user.status === 'active' ? 'text-danger-600 hover:text-danger-700' : 'text-success-600 hover:text-success-700'}`}
                              >
                                {user.status === 'active' ? <Ban className="w-4 h-4" /> : <CheckCircle className="w-4 h-4" />}
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* تبويب الصفقات */}
            {activeTab === 'trades' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">إدارة الصفقات</h3>
                  <div className="flex space-x-3 space-x-reverse">
                    <button className="btn btn-secondary btn-sm">
                      <Filter className="w-4 h-4 ml-2" />
                      فلترة
                    </button>
                    <button className="btn btn-secondary btn-sm">
                      <Download className="w-4 h-4 ml-2" />
                      تصدير
                    </button>
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-right py-3 px-4 font-medium text-gray-900">رقم الصفقة</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">المشتري</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">البائع</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">المبلغ</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">الحالة</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {trades.map((trade) => (
                        <tr key={trade.id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-3 px-4 text-sm font-medium text-gray-900">{trade.id}</td>
                          <td className="py-3 px-4 text-sm text-gray-600">{trade.buyer}</td>
                          <td className="py-3 px-4 text-sm text-gray-600">{trade.seller}</td>
                          <td className="py-3 px-4 text-sm text-gray-600">
                            {trade.amount} USDT ({trade.total} {trade.currency})
                          </td>
                          <td className="py-3 px-4">
                            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(trade.status)}`}>
                              {getStatusText(trade.status)}
                            </span>
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex space-x-2 space-x-reverse">
                              <button
                                onClick={() => alert(`عرض تفاصيل الصفقة ${trade.id}`)}
                                className="text-primary-600 hover:text-primary-700"
                              >
                                <Eye className="w-4 h-4" />
                              </button>
                              {trade.status === 'disputed' && (
                                <>
                                  <button
                                    onClick={() => handleTradeAction(trade.id, 'completed')}
                                    className="text-success-600 hover:text-success-700"
                                  >
                                    <CheckCircle className="w-4 h-4" />
                                  </button>
                                  <button
                                    onClick={() => handleTradeAction(trade.id, 'cancelled')}
                                    className="text-danger-600 hover:text-danger-700"
                                  >
                                    <X className="w-4 h-4" />
                                  </button>
                                </>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* تبويب النزاعات */}
            {activeTab === 'disputes' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-6">إدارة النزاعات</h3>
                <div className="text-center py-12">
                  <AlertTriangle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 text-lg">لا توجد نزاعات معلقة حالياً</p>
                  <p className="text-gray-400">سيتم عرض النزاعات هنا عند حدوثها</p>
                </div>
              </div>
            )}

            {/* تبويب الإعدادات */}
            {activeTab === 'settings' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">إعدادات المنصة</h3>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">إعدادات العمولة</h4>
                    <div>
                      <label className="form-label">نسبة العمولة (%)</label>
                      <input type="number" step="0.1" defaultValue="1.5" className="form-input" />
                    </div>
                    <div>
                      <label className="form-label">الحد الأدنى للعمولة</label>
                      <input type="number" defaultValue="1" className="form-input" />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">إعدادات الأمان</h4>
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">تفعيل المصادقة الثنائية</p>
                        <p className="text-sm text-gray-600">إجبار المستخدمين على تفعيل 2FA</p>
                      </div>
                      <input type="checkbox" defaultChecked className="toggle" />
                    </div>
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">التحقق من الهوية</p>
                        <p className="text-sm text-gray-600">طلب التحقق من الهوية للمبالغ الكبيرة</p>
                      </div>
                      <input type="checkbox" defaultChecked className="toggle" />
                    </div>
                  </div>
                </div>

                <div className="pt-6 border-t border-gray-200">
                  <button className="btn btn-primary">حفظ الإعدادات</button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
