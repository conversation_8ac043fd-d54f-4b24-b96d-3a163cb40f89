# منصة إيكاروس P2P - تبادل العملات الرقمية

منصة احترافية لتبادل عملة USDT بين الأفراد مباشرة مع ضمان العقود الذكية على شبكة BSC.

## 🌟 المميزات

- **أمان مضمون**: عقود ذكية على شبكة BSC تضمن حماية الأموال
- **سرعة فائقة**: معاملات تتم في دقائق معدودة
- **مجتمع موثوق**: تجار معتمدون ومراجعات حقيقية
- **أسعار تنافسية**: أفضل أسعار السوق
- **دعم 24/7**: فريق دعم متاح على مدار الساعة
- **تغطية شاملة**: خدمة في جميع أنحاء الوطن العربي

## 🚀 التقنيات المستخدمة

- **Frontend**: Next.js 15 + TypeScript
- **Styling**: TailwindCSS
- **Icons**: Lucide React
- **Fonts**: Cairo & Tajawal (Google Fonts)
- **Language**: Arabic (RTL Support)

## 📦 التثبيت والتشغيل

### المتطلبات
- Node.js 18+ 
- npm أو yarn

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/ikaros-p2p.git
cd ikaros-p2p
```

2. **تثبيت التبعيات**
```bash
npm install
# أو
yarn install
```

3. **تشغيل المشروع في وضع التطوير**
```bash
npm run dev
# أو
yarn dev
```

4. **فتح المتصفح**
افتح [http://localhost:3000](http://localhost:3000) لعرض المشروع.

## 🏗️ بنية المشروع

```
ikaros-p2p/
├── src/
│   ├── app/
│   │   ├── globals.css          # الأنماط العامة
│   │   ├── layout.tsx           # التخطيط الرئيسي
│   │   └── page.tsx             # الصفحة الرئيسية
│   └── components/
│       ├── Header.tsx           # مكون الهيدر
│       ├── HomePage.tsx         # مكون الصفحة الرئيسية
│       └── Footer.tsx           # مكون الفوتر
├── public/                      # الملفات العامة
├── tailwind.config.ts           # إعدادات TailwindCSS
├── next.config.ts               # إعدادات Next.js
└── package.json                 # تبعيات المشروع
```

## 🎨 التصميم والواجهة

### الألوان الرئيسية
- **Primary**: أزرق (#3b82f6)
- **Success**: أخضر (#22c55e)
- **Warning**: برتقالي (#f59e0b)
- **Danger**: أحمر (#ef4444)

### الخطوط
- **Cairo**: الخط الرئيسي للنصوص العربية
- **Tajawal**: خط بديل للنصوص العربية

### المكونات الرئيسية

#### Header
- شعار المنصة
- قائمة التنقل
- أزرار تسجيل الدخول/إنشاء حساب
- قائمة المستخدم (عند تسجيل الدخول)

#### HomePage
- قسم البطل (Hero Section)
- عرض العروض المتاحة
- قسم المميزات
- شرح كيفية عمل المنصة
- دعوة للعمل (CTA)

#### Footer
- معلومات الشركة
- روابط سريعة
- معلومات الاتصال
- روابط وسائل التواصل الاجتماعي

## 🔧 الإعدادات

### دعم RTL
المشروع يدعم الاتجاه من اليمين لليسار (RTL) بشكل كامل:

```css
html {
  direction: rtl;
  lang: ar;
}
```

### الخطوط العربية
تم تكوين الخطوط العربية في `layout.tsx`:

```typescript
const cairo = Cairo({
  variable: "--font-cairo",
  subsets: ["arabic", "latin"],
  display: "swap",
});
```

## 📱 الاستجابة (Responsive)

التصميم متجاوب بالكامل ويدعم:
- **Mobile**: أقل من 640px
- **Tablet**: 640px - 1024px  
- **Desktop**: أكبر من 1024px

## 🚀 النشر

### Vercel (موصى به)
```bash
npm run build
vercel --prod
```

### Netlify
```bash
npm run build
npm run export
# رفع مجلد out/ إلى Netlify
```

## 🔮 الميزات المستقبلية

- [x] صفحة تسجيل الدخول/إنشاء حساب ✅
- [x] صفحة العروض مع فلترة متقدمة ✅
- [ ] لوحة تحكم المستخدم
- [ ] صفحة إنشاء العروض
- [ ] نظام الدردشة المباشرة
- [ ] صفحة تنفيذ الصفقات
- [ ] نظام التقييمات
- [ ] لوحة الإدارة
- [ ] تكامل مع العقود الذكية
- [ ] نظام الإشعارات
- [ ] دعم عملات إضافية

## 🎯 المرحلة التالية - الصفحات المطلوبة:

### 1. لوحة تحكم المستخدم (Dashboard)
- عرض الصفقات الحالية والسابقة
- إحصائيات شخصية
- إدارة العروض
- الإعدادات الشخصية

### 2. صفحة إنشاء العروض (Create Offer)
- نموذج إنشاء عرض جديد
- تحديد السعر والكمية
- اختيار طرق الدفع
- شروط العرض

### 3. صفحة تنفيذ الصفقة (Trade Execution)
- دردشة مباشرة بين الطرفين
- تتبع حالة الصفقة
- رفع إثبات الدفع
- أزرار التأكيد والإلغاء

### 4. صفحة التقييمات (Reviews & Reputation)
- عرض تقييمات المستخدم
- تاريخ الصفقات
- نظام السمعة

### 5. لوحة الإدارة (Admin Dashboard)
- إدارة المستخدمين
- مراقبة الصفقات
- فض النزاعات
- إحصائيات المنصة

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 50 123 4567
- **الموقع**: [https://ikaros-p2p.com](https://ikaros-p2p.com)

## ⚠️ إخلاء المسؤولية

تداول العملات الرقمية ينطوي على مخاطر. يرجى التداول بحذر ومسؤولية. منصة إيكاروس P2P غير مسؤولة عن أي خسائر قد تحدث نتيجة التداول.

---

**تم تطوير هذا المشروع بـ ❤️ لخدمة المجتمع العربي**
