'use client';

import { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  Star, 
  Clock, 
  Shield, 
  TrendingUp,
  ChevronDown,
  MapPin,
  CreditCard
} from 'lucide-react';

// بيانات وهمية للعروض
const mockOffers = [
  {
    id: 1,
    seller: "أحمد محمد الأحمد",
    amount: 1000,
    minAmount: 100,
    maxAmount: 1000,
    price: 3.67,
    currency: "SAR",
    paymentMethods: ["تحويل بنكي", "حوالة سريعة"],
    rating: 4.9,
    trades: 156,
    completionRate: 98,
    avgTime: "5 دقائق",
    isOnline: true,
    country: "السعودية",
    verified: true
  },
  {
    id: 2,
    seller: "فاطمة علي السعيد",
    amount: 500,
    minAmount: 50,
    maxAmount: 500,
    price: 3.68,
    currency: "SAR",
    paymentMethods: ["حوالة سريعة", "تحويل فوري"],
    rating: 4.8,
    trades: 89,
    completionRate: 95,
    avgTime: "8 دقائق",
    isOnline: true,
    country: "الإمارات",
    verified: true
  },
  {
    id: 3,
    seller: "محمد السعيد",
    amount: 2000,
    minAmount: 200,
    maxAmount: 2000,
    price: 3.66,
    currency: "SAR",
    paymentMethods: ["تحويل بنكي"],
    rating: 5.0,
    trades: 234,
    completionRate: 99,
    avgTime: "3 دقائق",
    isOnline: false,
    country: "الكويت",
    verified: true
  },
  {
    id: 4,
    seller: "نورا أحمد",
    amount: 750,
    minAmount: 100,
    maxAmount: 750,
    price: 3.69,
    currency: "SAR",
    paymentMethods: ["حوالة سريعة", "تحويل بنكي", "كاش"],
    rating: 4.7,
    trades: 67,
    completionRate: 94,
    avgTime: "12 دقيقة",
    isOnline: true,
    country: "قطر",
    verified: false
  },
  {
    id: 5,
    seller: "خالد العتيبي",
    amount: 1500,
    minAmount: 150,
    maxAmount: 1500,
    price: 3.65,
    currency: "SAR",
    paymentMethods: ["تحويل بنكي", "حوالة سريعة"],
    rating: 4.9,
    trades: 198,
    completionRate: 97,
    avgTime: "6 دقائق",
    isOnline: true,
    country: "السعودية",
    verified: true
  }
];

const countries = ["جميع الدول", "السعودية", "الإمارات", "الكويت", "قطر", "البحرين", "عمان"];
const paymentMethods = ["جميع الطرق", "تحويل بنكي", "حوالة سريعة", "تحويل فوري", "كاش"];
const currencies = ["SAR", "AED", "KWD", "QAR"];

export default function OffersPage() {
  const [offers, setOffers] = useState(mockOffers);
  const [filteredOffers, setFilteredOffers] = useState(mockOffers);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCountry, setSelectedCountry] = useState('جميع الدول');
  const [selectedPayment, setSelectedPayment] = useState('جميع الطرق');
  const [selectedCurrency, setSelectedCurrency] = useState('SAR');
  const [minAmount, setMinAmount] = useState('');
  const [maxAmount, setMaxAmount] = useState('');
  const [sortBy, setSortBy] = useState('price');
  const [isLoading, setIsLoading] = useState(false);

  // تطبيق الفلاتر
  useEffect(() => {
    let filtered = offers;

    // فلتر البحث
    if (searchTerm) {
      filtered = filtered.filter(offer => 
        offer.seller.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // فلتر الدولة
    if (selectedCountry !== 'جميع الدول') {
      filtered = filtered.filter(offer => offer.country === selectedCountry);
    }

    // فلتر طريقة الدفع
    if (selectedPayment !== 'جميع الطرق') {
      filtered = filtered.filter(offer => 
        offer.paymentMethods.includes(selectedPayment)
      );
    }

    // فلتر المبلغ
    if (minAmount) {
      filtered = filtered.filter(offer => offer.maxAmount >= parseInt(minAmount));
    }
    if (maxAmount) {
      filtered = filtered.filter(offer => offer.minAmount <= parseInt(maxAmount));
    }

    // ترتيب النتائج
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return a.price - b.price;
        case 'rating':
          return b.rating - a.rating;
        case 'trades':
          return b.trades - a.trades;
        default:
          return 0;
      }
    });

    setFilteredOffers(filtered);
  }, [offers, searchTerm, selectedCountry, selectedPayment, minAmount, maxAmount, sortBy]);

  const handleBuyOffer = (offerId: number) => {
    setIsLoading(true);
    // محاكاة عملية الشراء
    setTimeout(() => {
      setIsLoading(false);
      alert(`تم بدء عملية الشراء للعرض رقم ${offerId}`);
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container-custom">
        {/* العنوان والإحصائيات */}
        <div className="mb-8">
          <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            العروض المتاحة
          </h1>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-white rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-primary-600">{filteredOffers.length}</div>
              <div className="text-sm text-gray-600">عرض متاح</div>
            </div>
            <div className="bg-white rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-success-600">
                {filteredOffers.filter(o => o.isOnline).length}
              </div>
              <div className="text-sm text-gray-600">تاجر متصل</div>
            </div>
            <div className="bg-white rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-warning-600">
                {Math.min(...filteredOffers.map(o => o.price)).toFixed(2)}
              </div>
              <div className="text-sm text-gray-600">أفضل سعر</div>
            </div>
            <div className="bg-white rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-danger-600">
                {(filteredOffers.reduce((sum, o) => sum + o.rating, 0) / filteredOffers.length).toFixed(1)}
              </div>
              <div className="text-sm text-gray-600">متوسط التقييم</div>
            </div>
          </div>
        </div>

        {/* أدوات البحث والفلترة */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            {/* البحث */}
            <div className="input-with-icon">
              <Search className="input-icon-right w-5 h-5" />
              <input
                type="text"
                placeholder="البحث عن تاجر..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input text-base-ar"
              />
            </div>

            {/* الدولة */}
            <select
              value={selectedCountry}
              onChange={(e) => setSelectedCountry(e.target.value)}
              className="form-input"
            >
              {countries.map(country => (
                <option key={country} value={country}>{country}</option>
              ))}
            </select>

            {/* طريقة الدفع */}
            <select
              value={selectedPayment}
              onChange={(e) => setSelectedPayment(e.target.value)}
              className="form-input"
            >
              {paymentMethods.map(method => (
                <option key={method} value={method}>{method}</option>
              ))}
            </select>

            {/* الترتيب */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="form-input"
            >
              <option value="price">ترتيب حسب السعر</option>
              <option value="rating">ترتيب حسب التقييم</option>
              <option value="trades">ترتيب حسب عدد الصفقات</option>
            </select>
          </div>

          {/* فلتر المبلغ */}
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label className="form-label">الحد الأدنى للمبلغ</label>
              <input
                type="number"
                placeholder="100"
                value={minAmount}
                onChange={(e) => setMinAmount(e.target.value)}
                className="form-input"
              />
            </div>
            <div>
              <label className="form-label">الحد الأقصى للمبلغ</label>
              <input
                type="number"
                placeholder="10000"
                value={maxAmount}
                onChange={(e) => setMaxAmount(e.target.value)}
                className="form-input"
              />
            </div>
          </div>
        </div>

        {/* قائمة العروض */}
        <div className="space-y-4">
          {filteredOffers.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg mb-4">لا توجد عروض تطابق معايير البحث</div>
              <button 
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCountry('جميع الدول');
                  setSelectedPayment('جميع الطرق');
                  setMinAmount('');
                  setMaxAmount('');
                }}
                className="btn btn-primary"
              >
                إعادة تعيين الفلاتر
              </button>
            </div>
          ) : (
            filteredOffers.map((offer) => (
              <div key={offer.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div className="grid lg:grid-cols-4 gap-6">
                  {/* معلومات التاجر */}
                  <div className="lg:col-span-1">
                    <div className="flex items-center mb-3">
                      <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                        <span className="text-primary-700 font-bold text-lg">
                          {offer.seller.charAt(0)}
                        </span>
                      </div>
                      <div className="mr-3">
                        <div className="flex items-center">
                          <h4 className="font-semibold text-gray-900">{offer.seller}</h4>
                          {offer.verified && (
                            <Shield className="w-4 h-4 text-success-500 mr-1" />
                          )}
                        </div>
                        <div className="flex items-center">
                          <Star className="w-4 h-4 text-yellow-400 fill-current" />
                          <span className="text-sm text-gray-600 mr-1">
                            {offer.rating} ({offer.trades} صفقة)
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center">
                        <MapPin className="w-4 h-4 text-gray-400 ml-2" />
                        <span className="text-gray-600">{offer.country}</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 text-gray-400 ml-2" />
                        <span className="text-gray-600">متوسط الوقت: {offer.avgTime}</span>
                      </div>
                      <div className="flex items-center">
                        <TrendingUp className="w-4 h-4 text-gray-400 ml-2" />
                        <span className="text-gray-600">معدل الإنجاز: {offer.completionRate}%</span>
                      </div>
                    </div>
                  </div>

                  {/* تفاصيل العرض */}
                  <div className="lg:col-span-2">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm text-gray-600">السعر</label>
                        <div className="text-2xl font-bold text-primary-600">
                          {offer.price} {offer.currency}
                        </div>
                      </div>
                      <div>
                        <label className="text-sm text-gray-600">الكمية المتاحة</label>
                        <div className="text-xl font-semibold text-gray-900">
                          {offer.amount.toLocaleString()} USDT
                        </div>
                      </div>
                      <div>
                        <label className="text-sm text-gray-600">الحد الأدنى</label>
                        <div className="text-lg text-gray-700">
                          {offer.minAmount.toLocaleString()} USDT
                        </div>
                      </div>
                      <div>
                        <label className="text-sm text-gray-600">الحد الأقصى</label>
                        <div className="text-lg text-gray-700">
                          {offer.maxAmount.toLocaleString()} USDT
                        </div>
                      </div>
                    </div>

                    <div className="mt-4">
                      <label className="text-sm text-gray-600 block mb-2">طرق الدفع</label>
                      <div className="flex flex-wrap gap-2">
                        {offer.paymentMethods.map((method, index) => (
                          <span 
                            key={index}
                            className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                          >
                            <CreditCard className="w-3 h-3 ml-1" />
                            {method}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* أزرار العمل */}
                  <div className="lg:col-span-1 flex flex-col justify-center">
                    <div className="flex items-center justify-center mb-4">
                      <div className={`w-3 h-3 rounded-full ${offer.isOnline ? 'bg-success-500' : 'bg-gray-400'} ml-2`}></div>
                      <span className={`text-sm font-medium ${offer.isOnline ? 'text-success-600' : 'text-gray-500'}`}>
                        {offer.isOnline ? 'متصل الآن' : 'غير متصل'}
                      </span>
                    </div>
                    
                    <button
                      onClick={() => handleBuyOffer(offer.id)}
                      disabled={!offer.isOnline || isLoading}
                      className={`btn w-full ${offer.isOnline ? 'btn-primary' : 'btn-secondary'} ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      {isLoading ? 'جاري المعالجة...' : 'شراء الآن'}
                    </button>
                    
                    <button className="btn btn-secondary w-full mt-2">
                      عرض التفاصيل
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* تحميل المزيد */}
        {filteredOffers.length > 0 && (
          <div className="text-center mt-8">
            <button className="btn btn-secondary">
              تحميل المزيد من العروض
            </button>
          </div>
        )}
      </div>
    </div>
  );
}