import type { <PERSON>ada<PERSON> } from "next";
import { Cairo, <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";

const cairo = Cairo({
  variable: "--font-cairo",
  subsets: ["arabic", "latin"],
  display: "swap",
});

const tajawal = Tajawal({
  variable: "--font-tajawal",
  subsets: ["arabic", "latin"],
  weight: ["200", "300", "400", "500", "700", "800", "900"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "إيكاروس P2P - منصة تبادل العملات الرقمية",
  description: "منصة آمنة وموثوقة لتبادل عملة USDT بين الأفراد مباشرة مع ضمان العقود الذكية",
  keywords: "USDT, تبادل عملات رقمية, P2P, عقود ذكية, BSC",
  authors: [{ name: "فريق إيكاروس" }],
  robots: "index, follow",
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body
        className={`${cairo.variable} ${tajawal.variable} font-arabic antialiased bg-gray-50 text-gray-900`}
      >
        {children}
      </body>
    </html>
  );
}
