'use client';

import { useState } from 'react';
import { Menu, <PERSON>, User, <PERSON>, <PERSON><PERSON>, LogOut } from 'lucide-react';

interface HeaderProps {
  isLoggedIn?: boolean;
  userName?: string;
}

export default function Header({ isLoggedIn = false, userName = "المستخدم" }: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleProfile = () => setIsProfileOpen(!isProfileOpen);

  return (
    <header className="bg-white/95 backdrop-blur-sm shadow-lg border-b border-gray-200 sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* الشعار */}
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-300">
                <span className="text-white font-bold text-xl">إ</span>
              </div>
              <div className="mr-3">
                <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">إيكاروس P2P</h1>
                <p className="text-xs text-gray-500">منصة تبادل العملات الرقمية</p>
              </div>
            </div>
          </div>

          {/* القائمة الرئيسية - سطح المكتب */}
          <nav className="hidden md:flex items-center space-x-8 space-x-reverse">
            <a href="/" className="relative text-gray-700 hover:text-blue-600 font-medium transition-all duration-300 group">
              الرئيسية
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
            </a>
            <a href="/offers" className="relative text-gray-700 hover:text-blue-600 font-medium transition-all duration-300 group">
              العروض
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
            </a>
            {isLoggedIn && (
              <a href="/dashboard" className="relative text-gray-700 hover:text-blue-600 font-medium transition-all duration-300 group">
                لوحة التحكم
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
              </a>
            )}
            <a href="/wallet" className="relative text-gray-700 hover:text-blue-600 font-medium transition-all duration-300 group">
              المحفظة
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
            </a>
            <a href="#" className="relative text-gray-700 hover:text-blue-600 font-medium transition-all duration-300 group">
              الدعم
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
            </a>
          </nav>

          {/* أزرار المستخدم */}
          <div className="flex items-center space-x-4 space-x-reverse">
            {isLoggedIn ? (
              <div className="flex items-center space-x-4 space-x-reverse">
                {/* الإشعارات */}
                <button className="relative p-2 text-gray-600 hover:text-primary-600 transition-colors">
                  <Bell className="w-5 h-5" />
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-danger-500 rounded-full"></span>
                </button>

                {/* قائمة المستخدم */}
                <div className="relative">
                  <button
                    onClick={toggleProfile}
                    className="flex items-center space-x-2 space-x-reverse p-2 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-primary-600" />
                    </div>
                    <span className="hidden md:block text-sm font-medium text-gray-700">
                      {userName}
                    </span>
                  </button>

                  {/* قائمة منسدلة للمستخدم */}
                  {isProfileOpen && (
                    <div className="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2">
                      <a href="/dashboard" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <User className="w-4 h-4 ml-3" />
                        لوحة التحكم
                      </a>
                      <a href="/reviews" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <User className="w-4 h-4 ml-3" />
                        التقييمات
                      </a>
                      <a href="#" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <Settings className="w-4 h-4 ml-3" />
                        الإعدادات
                      </a>
                      <hr className="my-2" />
                      <a href="#" className="flex items-center px-4 py-2 text-sm text-danger-600 hover:bg-gray-100">
                        <LogOut className="w-4 h-4 ml-3" />
                        تسجيل الخروج
                      </a>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-3 space-x-reverse">
                <a href="/login" className="px-4 py-2 text-gray-700 hover:text-blue-600 font-medium transition-colors rounded-lg hover:bg-gray-100">
                  تسجيل الدخول
                </a>
                <a href="/login" className="px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                  إنشاء حساب
                </a>
              </div>
            )}

            {/* زر القائمة للجوال */}
            <button
              onClick={toggleMenu}
              className="md:hidden p-2 text-gray-600 hover:text-primary-600 transition-colors"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* القائمة المنسدلة للجوال */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-200 py-4">
            <nav className="flex flex-col space-y-4">
              <a href="#" className="text-gray-700 hover:text-primary-600 font-medium transition-colors">
                الرئيسية
              </a>
              <a href="#" className="text-gray-700 hover:text-primary-600 font-medium transition-colors">
                العروض
              </a>
              <a href="#" className="text-gray-700 hover:text-primary-600 font-medium transition-colors">
                كيف يعمل
              </a>
              <a href="#" className="text-gray-700 hover:text-primary-600 font-medium transition-colors">
                الدعم
              </a>
              {!isLoggedIn && (
                <div className="flex flex-col space-y-2 pt-4 border-t border-gray-200">
                  <button className="btn btn-secondary text-sm">
                    تسجيل الدخول
                  </button>
                  <button className="btn btn-primary text-sm">
                    إنشاء حساب
                  </button>
                </div>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}