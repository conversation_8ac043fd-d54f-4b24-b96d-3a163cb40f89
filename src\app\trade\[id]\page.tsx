import Header from '@/components/Header';
import TradePage from '@/components/TradePage';
import Footer from '@/components/Footer';

interface TradePageProps {
  params: {
    id: string;
  };
}

export default function Trade({ params }: TradePageProps) {
  return (
    <div className="min-h-screen">
      <Header isLoggedIn={true} userName="أحمد محمد" />
      <TradePage tradeId={params.id} />
      <Footer />
    </div>
  );
}
