'use client';

import { Star, Shield, Clock, TrendingUp } from 'lucide-react';

interface Offer {
  id: number;
  seller: string;
  amount: number;
  price: number;
  currency: string;
  paymentMethod: string;
  rating: number;
  trades: number;
  isOnline: boolean;
}

interface OfferCardProps {
  offer: Offer;
  onBuy: (offerId: number) => void;
  featured?: boolean;
}

export default function OfferCard({ offer, onBuy, featured = false }: OfferCardProps) {
  return (
    <div className={`group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border overflow-hidden ${
      featured ? 'border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-white' : 'border-gray-100'
    }`}>
      {/* شريط علوي ملون */}
      <div className={`h-1 ${featured ? 'bg-gradient-to-r from-blue-500 to-purple-500' : 'bg-gradient-to-r from-gray-300 to-gray-400'}`}></div>
      
      {/* شارة المميز */}
      {featured && (
        <div className="absolute top-3 left-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
          مميز
        </div>
      )}
      
      <div className="p-6">
        {/* معلومات البائع */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className={`w-12 h-12 rounded-full flex items-center justify-center shadow-lg ${
              featured 
                ? 'bg-gradient-to-br from-blue-500 to-purple-600' 
                : 'bg-gradient-to-br from-gray-500 to-gray-600'
            }`}>
              <span className="text-white font-bold text-lg">
                {offer.seller.charAt(0)}
              </span>
            </div>
            <div className="mr-3">
              <h4 className={`font-semibold group-hover:text-blue-600 transition-colors ${
                featured ? 'text-blue-900' : 'text-gray-900'
              }`}>
                {offer.seller}
              </h4>
              <div className="flex items-center">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <span className="text-sm text-gray-600 mr-1">
                  {offer.rating} ({offer.trades} صفقة)
                </span>
                {offer.rating >= 4.8 && (
                  <Shield className="w-4 h-4 text-green-500 mr-1" title="بائع موثوق" />
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center">
            <div className={`w-3 h-3 rounded-full ${offer.isOnline ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
            <span className={`text-xs mr-2 font-medium ${offer.isOnline ? 'text-green-600' : 'text-gray-500'}`}>
              {offer.isOnline ? 'متصل' : 'غير متصل'}
            </span>
          </div>
        </div>

        {/* تفاصيل العرض */}
        <div className={`rounded-lg p-4 mb-4 ${featured ? 'bg-blue-50' : 'bg-gray-50'}`}>
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-600 text-sm">الكمية المتاحة</span>
            <span className="font-bold text-lg text-gray-900">{offer.amount.toLocaleString()} USDT</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600 text-sm">السعر</span>
            <span className={`font-bold text-xl ${featured ? 'text-blue-600' : 'text-blue-600'}`}>
              {offer.price} {offer.currency}
            </span>
          </div>
        </div>
        
        {/* طريقة الدفع */}
        <div className="flex items-center justify-between mb-6">
          <span className="text-gray-600 text-sm">طريقة الدفع:</span>
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
            featured 
              ? 'bg-blue-100 text-blue-800' 
              : 'bg-gray-100 text-gray-800'
          }`}>
            {offer.paymentMethod}
          </span>
        </div>

        {/* إحصائيات إضافية */}
        <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
          <div className="flex items-center">
            <Clock className="w-3 h-3 mr-1" />
            <span>متوسط الوقت: 5 دقائق</span>
          </div>
          <div className="flex items-center">
            <TrendingUp className="w-3 h-3 mr-1" />
            <span>معدل الإنجاز: 98%</span>
          </div>
        </div>

        {/* زر الشراء */}
        <button
          onClick={() => onBuy(offer.id)}
          disabled={!offer.isOnline}
          className={`w-full font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl ${
            offer.isOnline
              ? featured
                ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700'
                : 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          {offer.isOnline ? 'شراء الآن' : 'غير متاح'}
        </button>
      </div>

      {/* تأثير الهوفر */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
    </div>
  );
}
