'use client';

import { useState, useEffect } from 'react';
import {
  TrendingUp,
  DollarSign,
  Clock,
  Star,
  Eye,
  Edit,
  Trash2,
  Plus,
  Filter,
  Download,
  Settings,
  Bell,
  Shield,
  Activity
} from 'lucide-react';

// بيانات وهمية للمستخدم
const userData = {
  name: "أحمد محمد الأحمد",
  email: "<EMAIL>",
  phone: "+966 50 123 4567",
  rating: 4.8,
  totalTrades: 156,
  completionRate: 98,
  totalVolume: 2847392,
  activeOffers: 3,
  joinedAt: "2023-01-15",
  isVerified: true
};

// بيانات وهمية للصفقات
const mockTrades = [
  {
    id: "T001",
    type: "buy",
    amount: 1000,
    price: 3.67,
    currency: "SAR",
    status: "completed",
    partner: "فاطمة علي",
    createdAt: "2024-01-15T10:30:00Z",
    completedAt: "2024-01-15T10:45:00Z"
  },
  {
    id: "T002",
    type: "sell",
    amount: 500,
    price: 3.68,
    currency: "SAR",
    status: "active",
    partner: "محمد السعيد",
    createdAt: "2024-01-14T14:20:00Z"
  },
  {
    id: "T003",
    type: "buy",
    amount: 750,
    price: 3.66,
    currency: "SAR",
    status: "cancelled",
    partner: "نورا أحمد",
    createdAt: "2024-01-13T09:15:00Z"
  }
];

// بيانات وهمية للعروض
const mockOffers = [
  {
    id: "O001",
    type: "sell",
    amount: 1000,
    price: 3.67,
    currency: "SAR",
    paymentMethods: ["تحويل بنكي", "حوالة سريعة"],
    isActive: true,
    views: 45,
    createdAt: "2024-01-10T08:00:00Z"
  },
  {
    id: "O002",
    type: "buy",
    amount: 500,
    price: 3.69,
    currency: "SAR",
    paymentMethods: ["حوالة سريعة"],
    isActive: true,
    views: 23,
    createdAt: "2024-01-12T12:30:00Z"
  }
];

export default function DashboardPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [trades, setTrades] = useState(mockTrades);
  const [offers, setOffers] = useState(mockOffers);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-success-600 bg-success-100';
      case 'active':
        return 'text-warning-600 bg-warning-100';
      case 'cancelled':
        return 'text-danger-600 bg-danger-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'active':
        return 'نشطة';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container-custom">
        {/* ترحيب وإحصائيات سريعة */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                مرحباً، {userData.name.split(' ')[0]}! 👋
              </h1>
              <p className="text-gray-600">إليك نظرة سريعة على نشاطك في المنصة</p>
            </div>
            <div className="flex space-x-3 space-x-reverse mt-4 md:mt-0">
              <button className="btn btn-secondary">
                <Settings className="w-4 h-4 ml-2" />
                الإعدادات
              </button>
              <a href="/create-offer" className="btn btn-primary">
                <Plus className="w-4 h-4 ml-2" />
                إنشاء عرض جديد
              </a>
            </div>
          </div>

          {/* بطاقات الإحصائيات */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">إجمالي الصفقات</p>
                  <p className="text-2xl font-bold text-gray-900">{userData.totalTrades}</p>
                </div>
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-primary-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">إجمالي الحجم</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {(userData.totalVolume / 1000000).toFixed(1)}M
                  </p>
                </div>
                <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-success-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">التقييم</p>
                  <p className="text-2xl font-bold text-gray-900">{userData.rating}</p>
                </div>
                <div className="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center">
                  <Star className="w-6 h-6 text-warning-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">معدل الإنجاز</p>
                  <p className="text-2xl font-bold text-gray-900">{userData.completionRate}%</p>
                </div>
                <div className="w-12 h-12 bg-danger-100 rounded-lg flex items-center justify-center">
                  <Shield className="w-6 h-6 text-danger-600" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* التبويبات */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 space-x-reverse px-6">
              {[
                { id: 'overview', label: 'نظرة عامة', icon: Activity },
                { id: 'trades', label: 'الصفقات', icon: TrendingUp },
                { id: 'offers', label: 'عروضي', icon: DollarSign },
                { id: 'settings', label: 'الإعدادات', icon: Settings }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="w-4 h-4 ml-2" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {/* تبويب النظرة العامة */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  {/* الصفقات الأخيرة */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">الصفقات الأخيرة</h3>
                    <div className="space-y-3">
                      {trades.slice(0, 3).map((trade) => (
                        <div key={trade.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">
                              {trade.type === 'buy' ? 'شراء' : 'بيع'} {trade.amount} USDT
                            </p>
                            <p className="text-sm text-gray-600">مع {trade.partner}</p>
                          </div>
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(trade.status)}`}>
                            {getStatusText(trade.status)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* العروض النشطة */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">العروض النشطة</h3>
                    <div className="space-y-3">
                      {offers.filter(offer => offer.isActive).map((offer) => (
                        <div key={offer.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">
                              {offer.type === 'buy' ? 'شراء' : 'بيع'} {offer.amount} USDT
                            </p>
                            <p className="text-sm text-gray-600">بسعر {offer.price} {offer.currency}</p>
                          </div>
                          <div className="flex items-center text-sm text-gray-500">
                            <Eye className="w-4 h-4 ml-1" />
                            {offer.views}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* تبويب الصفقات */}
            {activeTab === 'trades' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">جميع الصفقات</h3>
                  <div className="flex space-x-3 space-x-reverse">
                    <button className="btn btn-secondary btn-sm">
                      <Filter className="w-4 h-4 ml-2" />
                      فلترة
                    </button>
                    <button className="btn btn-secondary btn-sm">
                      <Download className="w-4 h-4 ml-2" />
                      تصدير
                    </button>
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-right py-3 px-4 font-medium text-gray-900">رقم الصفقة</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">النوع</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">المبلغ</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">السعر</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">الطرف الآخر</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">الحالة</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">التاريخ</th>
                      </tr>
                    </thead>
                    <tbody>
                      {trades.map((trade) => (
                        <tr key={trade.id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-3 px-4 text-sm font-medium text-gray-900">{trade.id}</td>
                          <td className="py-3 px-4 text-sm text-gray-600">
                            {trade.type === 'buy' ? 'شراء' : 'بيع'}
                          </td>
                          <td className="py-3 px-4 text-sm text-gray-600">{trade.amount} USDT</td>
                          <td className="py-3 px-4 text-sm text-gray-600">{trade.price} {trade.currency}</td>
                          <td className="py-3 px-4 text-sm text-gray-600">{trade.partner}</td>
                          <td className="py-3 px-4">
                            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(trade.status)}`}>
                              {getStatusText(trade.status)}
                            </span>
                          </td>
                          <td className="py-3 px-4 text-sm text-gray-600">
                            {new Date(trade.createdAt).toLocaleDateString('ar-SA')}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* تبويب العروض */}
            {activeTab === 'offers' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">عروضي</h3>
                  <a href="/create-offer" className="btn btn-primary">
                    <Plus className="w-4 h-4 ml-2" />
                    إنشاء عرض جديد
                  </a>
                </div>

                <div className="space-y-4">
                  {offers.map((offer) => (
                    <div key={offer.id} className="bg-gray-50 rounded-lg p-6">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center mb-2">
                            <h4 className="text-lg font-semibold text-gray-900">
                              {offer.type === 'buy' ? 'شراء' : 'بيع'} {offer.amount} USDT
                            </h4>
                            <span className={`mr-3 px-3 py-1 rounded-full text-xs font-medium ${
                              offer.isActive ? 'bg-success-100 text-success-600' : 'bg-gray-100 text-gray-600'
                            }`}>
                              {offer.isActive ? 'نشط' : 'غير نشط'}
                            </span>
                          </div>
                          <p className="text-gray-600 mb-2">السعر: {offer.price} {offer.currency}</p>
                          <p className="text-sm text-gray-500">
                            طرق الدفع: {offer.paymentMethods.join(', ')}
                          </p>
                          <div className="flex items-center mt-3 text-sm text-gray-500">
                            <Eye className="w-4 h-4 ml-1" />
                            {offer.views} مشاهدة
                            <Clock className="w-4 h-4 mr-4 ml-1" />
                            {new Date(offer.createdAt).toLocaleDateString('ar-SA')}
                          </div>
                        </div>
                        <div className="flex space-x-2 space-x-reverse">
                          <button className="btn btn-secondary btn-sm">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="btn btn-secondary btn-sm text-danger-600 hover:bg-danger-50">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* تبويب الإعدادات */}
            {activeTab === 'settings' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">إعدادات الحساب</h3>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">المعلومات الشخصية</h4>
                    <div>
                      <label className="form-label">الاسم الكامل</label>
                      <input type="text" value={userData.name} className="form-input" />
                    </div>
                    <div>
                      <label className="form-label">البريد الإلكتروني</label>
                      <input type="email" value={userData.email} className="form-input" />
                    </div>
                    <div>
                      <label className="form-label">رقم الهاتف</label>
                      <input type="tel" value={userData.phone} className="form-input" />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">إعدادات الأمان</h4>
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">المصادقة الثنائية</p>
                        <p className="text-sm text-gray-600">حماية إضافية لحسابك</p>
                      </div>
                      <button className="btn btn-primary btn-sm">تفعيل</button>
                    </div>
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">إشعارات البريد الإلكتروني</p>
                        <p className="text-sm text-gray-600">تلقي تحديثات الصفقات</p>
                      </div>
                      <input type="checkbox" defaultChecked className="toggle" />
                    </div>
                  </div>
                </div>

                <div className="pt-6 border-t border-gray-200">
                  <button className="btn btn-primary">حفظ التغييرات</button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
