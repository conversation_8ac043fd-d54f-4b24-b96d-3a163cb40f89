'use client';

import { useState } from 'react';
import {
  Star,
  ThumbsUp,
  ThumbsDown,
  Filter,
  Search,
  Calendar,
  User,
  TrendingUp,
  Award,
  Shield,
  Clock
} from 'lucide-react';

// بيانات وهمية للمستخدم
const userData = {
  name: "أحم<PERSON> محمد الأحمد",
  rating: 4.8,
  totalTrades: 156,
  positiveReviews: 148,
  neutralReviews: 6,
  negativeReviews: 2,
  completionRate: 98,
  avgResponseTime: "5 دقائق",
  joinedAt: "2023-01-15",
  isVerified: true
};

// بيانات وهمية للتقييمات
const mockReviews = [
  {
    id: "R001",
    reviewer: "فاطمة علي",
    rating: 5,
    comment: "تاجر ممتاز، سريع في الرد والتحويل تم بسلاسة. أنصح بالتعامل معه.",
    tradeAmount: 1000,
    tradeDate: "2024-01-10T14:30:00Z",
    type: "sell"
  },
  {
    id: "R002",
    reviewer: "محمد السعيد",
    rating: 5,
    comment: "صفقة ممتازة، التحويل سريع والتعامل احترافي جداً.",
    tradeAmount: 500,
    tradeDate: "2024-01-08T10:15:00Z",
    type: "buy"
  },
  {
    id: "R003",
    reviewer: "نورا أحمد",
    rating: 4,
    comment: "تعامل جيد لكن كان هناك تأخير بسيط في الرد.",
    tradeAmount: 750,
    tradeDate: "2024-01-05T16:45:00Z",
    type: "sell"
  },
  {
    id: "R004",
    reviewer: "خالد العتيبي",
    rating: 5,
    comment: "من أفضل التجار في المنصة، أنصح بالتعامل معه بقوة.",
    tradeAmount: 2000,
    tradeDate: "2024-01-03T09:20:00Z",
    type: "buy"
  },
  {
    id: "R005",
    reviewer: "سارة محمد",
    rating: 4,
    comment: "تعامل طيب ومهذب، الصفقة تمت بنجاح.",
    tradeAmount: 300,
    tradeDate: "2023-12-28T13:10:00Z",
    type: "sell"
  }
];

export default function ReviewsPage() {
  const [reviews, setReviews] = useState(mockReviews);
  const [filteredReviews, setFilteredReviews] = useState(mockReviews);
  const [filterRating, setFilterRating] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // تطبيق الفلاتر
  const applyFilters = () => {
    let filtered = reviews;

    // فلتر التقييم
    if (filterRating !== 'all') {
      const rating = parseInt(filterRating);
      filtered = filtered.filter(review => review.rating === rating);
    }

    // فلتر نوع الصفقة
    if (filterType !== 'all') {
      filtered = filtered.filter(review => review.type === filterType);
    }

    // فلتر البحث
    if (searchTerm) {
      filtered = filtered.filter(review => 
        review.reviewer.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.comment.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredReviews(filtered);
  };

  // تطبيق الفلاتر عند تغيير القيم
  useState(() => {
    applyFilters();
  });

  const getRatingDistribution = () => {
    const distribution = [0, 0, 0, 0, 0];
    reviews.forEach(review => {
      distribution[review.rating - 1]++;
    });
    return distribution.reverse(); // ترتيب من 5 إلى 1
  };

  const renderStars = (rating: number, size: string = 'w-4 h-4') => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${size} ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container-custom max-w-6xl">
        {/* معلومات المستخدم */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between">
            <div className="flex items-center mb-4 lg:mb-0">
              <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center ml-4">
                <User className="w-10 h-10 text-primary-600" />
              </div>
              <div>
                <div className="flex items-center mb-2">
                  <h1 className="text-2xl font-bold text-gray-900 ml-2">{userData.name}</h1>
                  {userData.isVerified && (
                    <Shield className="w-6 h-6 text-success-500" />
                  )}
                </div>
                <div className="flex items-center mb-2">
                  {renderStars(Math.round(userData.rating), 'w-5 h-5')}
                  <span className="text-lg font-semibold text-gray-900 mr-2">
                    {userData.rating}
                  </span>
                  <span className="text-gray-600">({userData.totalTrades} صفقة)</span>
                </div>
                <div className="text-sm text-gray-600">
                  عضو منذ {formatDate(userData.joinedAt)}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-2xl font-bold text-success-600">{userData.completionRate}%</div>
                <div className="text-xs text-gray-600">معدل الإنجاز</div>
              </div>
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-2xl font-bold text-primary-600">{userData.avgResponseTime}</div>
                <div className="text-xs text-gray-600">متوسط الرد</div>
              </div>
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-2xl font-bold text-warning-600">{userData.positiveReviews}</div>
                <div className="text-xs text-gray-600">تقييم إيجابي</div>
              </div>
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-2xl font-bold text-gray-600">{userData.totalTrades}</div>
                <div className="text-xs text-gray-600">إجمالي الصفقات</div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* الشريط الجانبي */}
          <div className="space-y-6">
            {/* توزيع التقييمات */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="font-semibold text-gray-900 mb-4">توزيع التقييمات</h3>
              
              <div className="space-y-3">
                {getRatingDistribution().map((count, index) => {
                  const rating = 5 - index;
                  const percentage = reviews.length > 0 ? (count / reviews.length) * 100 : 0;
                  
                  return (
                    <div key={rating} className="flex items-center">
                      <div className="flex items-center w-12">
                        <span className="text-sm text-gray-600 ml-1">{rating}</span>
                        <Star className="w-3 h-3 text-yellow-400 fill-current" />
                      </div>
                      <div className="flex-1 mx-3">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-yellow-400 h-2 rounded-full"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                      <span className="text-sm text-gray-600 w-8">{count}</span>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* الفلاتر */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="font-semibold text-gray-900 mb-4 flex items-center">
                <Filter className="w-4 h-4 ml-2" />
                الفلاتر
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="form-label">التقييم</label>
                  <select
                    value={filterRating}
                    onChange={(e) => setFilterRating(e.target.value)}
                    className="form-input"
                  >
                    <option value="all">جميع التقييمات</option>
                    <option value="5">5 نجوم</option>
                    <option value="4">4 نجوم</option>
                    <option value="3">3 نجوم</option>
                    <option value="2">2 نجوم</option>
                    <option value="1">1 نجمة</option>
                  </select>
                </div>

                <div>
                  <label className="form-label">نوع الصفقة</label>
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    className="form-input"
                  >
                    <option value="all">جميع الصفقات</option>
                    <option value="buy">شراء</option>
                    <option value="sell">بيع</option>
                  </select>
                </div>

                <button
                  onClick={applyFilters}
                  className="btn btn-primary w-full"
                >
                  تطبيق الفلاتر
                </button>
              </div>
            </div>

            {/* الإنجازات */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="font-semibold text-gray-900 mb-4 flex items-center">
                <Award className="w-4 h-4 ml-2" />
                الإنجازات
              </h3>
              
              <div className="space-y-3">
                <div className="flex items-center p-3 bg-yellow-50 rounded-lg">
                  <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center ml-3">
                    <Star className="w-4 h-4 text-yellow-600" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">تاجر موثوق</div>
                    <div className="text-xs text-gray-600">أكثر من 100 صفقة ناجحة</div>
                  </div>
                </div>

                <div className="flex items-center p-3 bg-blue-50 rounded-lg">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                    <Clock className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">استجابة سريعة</div>
                    <div className="text-xs text-gray-600">متوسط رد أقل من 10 دقائق</div>
                  </div>
                </div>

                <div className="flex items-center p-3 bg-green-50 rounded-lg">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center ml-3">
                    <TrendingUp className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">معدل إنجاز عالي</div>
                    <div className="text-xs text-gray-600">أكثر من 95% إنجاز</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* التقييمات */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4 md:mb-0">
                    التقييمات ({filteredReviews.length})
                  </h2>
                  
                  <div className="input-with-icon w-64">
                    <Search className="input-icon-right w-4 h-4" />
                    <input
                      type="text"
                      placeholder="البحث في التقييمات..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="form-input text-base-ar"
                    />
                  </div>
                </div>
              </div>

              <div className="divide-y divide-gray-200">
                {filteredReviews.length === 0 ? (
                  <div className="p-8 text-center">
                    <div className="text-gray-500 text-lg mb-2">لا توجد تقييمات</div>
                    <p className="text-gray-400">لا توجد تقييمات تطابق معايير البحث</p>
                  </div>
                ) : (
                  filteredReviews.map((review) => (
                    <div key={review.id} className="p-6">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center ml-3">
                            <User className="w-5 h-5 text-gray-600" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{review.reviewer}</div>
                            <div className="flex items-center">
                              {renderStars(review.rating)}
                              <span className="text-sm text-gray-600 mr-2">
                                {formatDate(review.tradeDate)}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <div className="text-sm text-gray-600">
                            {review.type === 'buy' ? 'شراء' : 'بيع'} {review.tradeAmount} USDT
                          </div>
                        </div>
                      </div>
                      
                      <p className="text-gray-700 leading-relaxed mb-4">
                        {review.comment}
                      </p>
                      
                      <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                        <button className="flex items-center hover:text-success-600 transition-colors">
                          <ThumbsUp className="w-4 h-4 ml-1" />
                          مفيد
                        </button>
                        <button className="flex items-center hover:text-danger-600 transition-colors">
                          <ThumbsDown className="w-4 h-4 ml-1" />
                          غير مفيد
                        </button>
                      </div>
                    </div>
                  ))
                )}
              </div>

              {filteredReviews.length > 0 && (
                <div className="p-6 border-t border-gray-200 text-center">
                  <button className="btn btn-secondary">
                    تحميل المزيد من التقييمات
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
