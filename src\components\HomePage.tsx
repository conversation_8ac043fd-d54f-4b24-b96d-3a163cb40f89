'use client';

import { useState, useEffect } from 'react';
import {
  Shield,
  Zap,
  Users,
  TrendingUp,
  ArrowRight,
  Star,
  CheckCircle,
  DollarSign,
  Clock,
  Globe
} from 'lucide-react';
import { DefaultPlatformStats } from './AnimatedStats';
import OfferCard from './OfferCard';
import { GradientButton, OutlineButton } from './Button';
import { OffersLoader } from './LoadingSpinner';

// بيانات وهمية للعروض
const mockOffers = [
  {
    id: 1,
    seller: "أحمد محمد",
    amount: 1000,
    price: 3.67,
    currency: "SAR",
    paymentMethod: "تحويل بنكي",
    rating: 4.9,
    trades: 156,
    isOnline: true
  },
  {
    id: 2,
    seller: "فاطمة علي",
    amount: 500,
    price: 3.68,
    currency: "SAR",
    paymentMethod: "حوالة سريعة",
    rating: 4.8,
    trades: 89,
    isOnline: true
  },
  {
    id: 3,
    seller: "محمد السعيد",
    amount: 2000,
    price: 3.66,
    currency: "SAR",
    paymentMethod: "تحويل بنكي",
    rating: 5.0,
    trades: 234,
    isOnline: false
  }
];

// إحصائيات المنصة
const platformStats = {
  totalTrades: 12547,
  totalUsers: 8932,
  totalVolume: 2847392,
  averageRating: 4.8
};

export default function HomePage() {
  const [currentOffers, setCurrentOffers] = useState(mockOffers);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // محاكاة تحميل البيانات
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleBuyOffer = (offerId: number) => {
    setIsLoading(true);
    // محاكاة عملية الشراء
    setTimeout(() => {
      setIsLoading(false);
      alert(`تم بدء عملية الشراء للعرض رقم ${offerId}`);
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* القسم الرئيسي */}
      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white py-20 overflow-hidden">
        {/* خلفية متحركة */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20"></div>
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full animate-pulse"></div>
          <div className="absolute top-32 right-20 w-16 h-16 bg-white/5 rounded-full animate-pulse" style={{animationDelay: '1s'}}></div>
          <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-white/10 rounded-full animate-pulse" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="animate-fade-in">
              <div className="inline-flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
                <Shield className="w-4 h-4 ml-2" />
                <span className="text-sm">منصة آمنة ومرخصة</span>
              </div>
              <h1 className="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
                تبادل آمن وسريع
                <span className="block bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">لعملة USDT</span>
              </h1>
              <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                منصة إيكاروس P2P توفر لك تبادل عملة USDT بأمان تام مع ضمان العقود الذكية على شبكة BSC
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <a href="/offers">
                  <GradientButton
                    size="xl"
                    icon={ArrowRight}
                    iconPosition="right"
                    className="bg-white text-blue-700 hover:bg-gray-100 w-full"
                  >
                    ابدأ التداول الآن
                  </GradientButton>
                </a>
                <a href="/login">
                  <OutlineButton
                    size="xl"
                    className="border-2 border-white text-white hover:bg-white hover:text-blue-700 w-full"
                  >
                    إنشاء حساب مجاني
                  </OutlineButton>
                </a>
              </div>
            </div>

            <div className="animate-slide-up">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 shadow-2xl">
                <h3 className="text-2xl font-bold mb-8 text-center">إحصائيات المنصة</h3>
                <DefaultPlatformStats />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* قسم العروض الحية */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <div className="inline-flex items-center bg-blue-100 text-blue-800 rounded-full px-4 py-2 mb-4">
              <TrendingUp className="w-4 h-4 ml-2" />
              <span className="text-sm font-medium">العروض الأكثر طلباً</span>
            </div>
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              العروض المتاحة الآن
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              اختر من بين أفضل العروض المتاحة من تجار موثوقين مع أفضل الأسعار
            </p>
          </div>

          {isLoading ? (
            <OffersLoader />
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {currentOffers.map((offer, index) => (
                <OfferCard
                  key={offer.id}
                  offer={offer}
                  onBuy={handleBuyOffer}
                  featured={index === 0} // العرض الأول مميز
                />
              ))}
            </div>
          )}

          <div className="text-center mt-8">
            <a href="/offers" className="btn btn-secondary">
              عرض جميع العروض
              <ArrowRight className="w-4 h-4 mr-2" />
            </a>
          </div>
        </div>
      </section>

      {/* قسم المميزات */}
      <section className="py-20 bg-white relative overflow-hidden">
        {/* خلفية هندسية */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-600 to-purple-600"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center bg-blue-100 text-blue-800 rounded-full px-4 py-2 mb-4">
              <CheckCircle className="w-4 h-4 ml-2" />
              <span className="text-sm font-medium">مميزات حصرية</span>
            </div>
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              لماذا تختار إيكاروس P2P؟
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              نوفر لك أفضل تجربة تداول آمنة وموثوقة مع أحدث التقنيات
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="group text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <Shield className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">أمان مضمون</h3>
              <p className="text-gray-600 leading-relaxed">
                عقود ذكية على شبكة BSC تضمن حماية أموالك بنسبة 100% مع أعلى معايير الأمان
              </p>
            </div>

            <div className="group text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100" style={{ animationDelay: '0.1s' }}>
              <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <Zap className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-green-600 transition-colors">سرعة فائقة</h3>
              <p className="text-gray-600 leading-relaxed">
                معاملات سريعة تتم في دقائق معدودة مع أقل رسوم ممكنة وأفضل أداء
              </p>
            </div>

            <div className="group text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100" style={{ animationDelay: '0.2s' }}>
              <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <Users className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-purple-600 transition-colors">مجتمع موثوق</h3>
              <p className="text-gray-600 leading-relaxed">
                تجار معتمدون ومراجعات حقيقية لضمان أفضل تجربة تداول آمنة
              </p>
            </div>

            <div className="text-center animate-slide-up" style={{ animationDelay: '0.3s' }}>
              <div className="w-16 h-16 bg-danger-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="w-8 h-8 text-danger-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">أسعار تنافسية</h3>
              <p className="text-gray-600">
                أفضل أسعار السوق مع إمكانية المقارنة بين العروض المختلفة
              </p>
            </div>

            <div className="text-center animate-slide-up" style={{ animationDelay: '0.4s' }}>
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">دعم 24/7</h3>
              <p className="text-gray-600">
                فريق دعم متاح على مدار الساعة لمساعدتك في أي وقت
              </p>
            </div>

            <div className="text-center animate-slide-up" style={{ animationDelay: '0.5s' }}>
              <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Globe className="w-8 h-8 text-success-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">تغطية شاملة</h3>
              <p className="text-gray-600">
                خدمة متاحة في جميع أنحاء الوطن العربي مع دعم عملات محلية
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* قسم كيف يعمل */}
      <section className="bg-gray-50 section-padding">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              كيف تعمل المنصة؟
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              عملية بسيطة وآمنة في خطوات قليلة
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl font-bold">1</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">اختر العرض</h3>
              <p className="text-gray-600">
                تصفح العروض المتاحة واختر الأنسب لك من حيث السعر وطريقة الدفع
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl font-bold">2</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">أكمل الدفع</h3>
              <p className="text-gray-600">
                قم بتحويل المبلغ للبائع خارج المنصة وأكد إتمام التحويل
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl font-bold">3</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">استلم USDT</h3>
              <p className="text-gray-600">
                بعد تأكيد البائع، ستحصل على عملة USDT في محفظتك فوراً
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* قسم الدعوة للعمل */}
      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700 text-white py-20 overflow-hidden">
        {/* خلفية زخرفية */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-40 h-40 bg-white rounded-full filter blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-60 h-60 bg-white rounded-full filter blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-white rounded-full filter blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <h2 className="text-3xl lg:text-5xl font-bold mb-6">
            ابدأ رحلتك في التداول الآن
          </h2>
          <p className="text-xl text-blue-100 mb-10 max-w-2xl mx-auto leading-relaxed">
            انضم إلى آلاف المستخدمين الذين يثقون في منصة إيكاروس P2P واستمتع بتجربة تداول آمنة ومربحة
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <a href="/login">
              <GradientButton
                size="xl"
                className="bg-white text-blue-700 hover:bg-gray-100 shadow-2xl w-full"
              >
                إنشاء حساب مجاني
              </GradientButton>
            </a>
            <a href="/offers">
              <OutlineButton
                size="xl"
                icon={ArrowRight}
                className="border-2 border-white text-white hover:bg-white hover:text-blue-700 w-full"
              >
                تصفح العروض
              </OutlineButton>
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}