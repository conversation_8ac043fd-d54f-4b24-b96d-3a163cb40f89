'use client';

import { 
  Mail, 
  Phone, 
  MapPin, 
  Facebook, 
  Twitter, 
  Instagram, 
  Linkedin,
  Shield,
  Clock,
  Users
} from 'lucide-react';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-white">
      {/* القسم الرئيسي */}
      <div className="container-custom py-12">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* معلومات الشركة */}
          <div className="lg:col-span-2">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">إ</span>
              </div>
              <div className="mr-3">
                <h3 className="text-2xl font-bold heading-arabic">إيكاروس P2P</h3>
                <p className="text-gray-400 text-sm body-arabic">منصة تبادل العملات الرقمية</p>
              </div>
            </div>
            
            <p className="text-gray-300 mb-6 leading-relaxed body-arabic">
              منصة إيكاروس P2P هي الحل الأمثل لتبادل عملة USDT بأمان وسرعة.
              نوفر لك بيئة آمنة ومضمونة بالعقود الذكية على شبكة BSC لضمان حماية أموالك.
            </p>

            <div className="flex space-x-4 space-x-reverse">
              <a href="#" className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors">
                <Facebook className="w-5 h-5" />
              </a>
              <a href="#" className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="#" className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors">
                <Instagram className="w-5 h-5" />
              </a>
              <a href="#" className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors">
                <Linkedin className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* روابط سريعة */}
          <div>
            <h4 className="text-lg font-bold mb-6 heading-arabic">روابط سريعة</h4>
            <ul className="space-y-3">
              <li>
                <a href="#" className="text-gray-300 hover:text-primary-400 transition-colors">
                  الرئيسية
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-primary-400 transition-colors">
                  العروض
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-primary-400 transition-colors">
                  كيف يعمل
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-primary-400 transition-colors">
                  الأسعار
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-primary-400 transition-colors">
                  المساعدة
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-primary-400 transition-colors">
                  اتصل بنا
                </a>
              </li>
            </ul>
          </div>

          {/* معلومات الاتصال */}
          <div>
            <h4 className="text-lg font-bold mb-6 heading-arabic">تواصل معنا</h4>
            <div className="space-y-4">
              <div className="flex items-center">
                <Mail className="w-5 h-5 text-primary-400 ml-3" />
                <span className="text-gray-300"><EMAIL></span>
              </div>
              <div className="flex items-center">
                <Phone className="w-5 h-5 text-primary-400 ml-3" />
                <span className="text-gray-300">+966 50 123 4567</span>
              </div>
              <div className="flex items-center">
                <MapPin className="w-5 h-5 text-primary-400 ml-3" />
                <span className="text-gray-300">الرياض، المملكة العربية السعودية</span>
              </div>
            </div>

            <div className="mt-6">
              <h5 className="font-semibold mb-3">ساعات العمل</h5>
              <div className="flex items-center text-gray-300">
                <Clock className="w-4 h-4 text-primary-400 ml-2" />
                <span className="text-sm">24/7 دعم متواصل</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* قسم الإحصائيات */}
      <div className="border-t border-gray-800">
        <div className="container-custom py-8">
          <div className="grid md:grid-cols-3 gap-6 text-center">
            <div className="flex items-center justify-center">
              <Shield className="w-8 h-8 text-primary-400 ml-3" />
              <div>
                <div className="text-2xl font-bold">100%</div>
                <div className="text-gray-400 text-sm">أمان مضمون</div>
              </div>
            </div>
            <div className="flex items-center justify-center">
              <Users className="w-8 h-8 text-primary-400 ml-3" />
              <div>
                <div className="text-2xl font-bold">8,932+</div>
                <div className="text-gray-400 text-sm">مستخدم نشط</div>
              </div>
            </div>
            <div className="flex items-center justify-center">
              <Clock className="w-8 h-8 text-primary-400 ml-3" />
              <div>
                <div className="text-2xl font-bold">&lt; 5 دقائق</div>
                <div className="text-gray-400 text-sm">متوسط وقت المعاملة</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* قسم الروابط القانونية */}
      <div className="border-t border-gray-800">
        <div className="container-custom py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 text-sm mb-4 md:mb-0">
              © {currentYear} إيكاروس P2P. جميع الحقوق محفوظة.
            </div>
            <div className="flex space-x-6 space-x-reverse text-sm">
              <a href="#" className="text-gray-400 hover:text-primary-400 transition-colors">
                سياسة الخصوصية
              </a>
              <a href="#" className="text-gray-400 hover:text-primary-400 transition-colors">
                شروط الاستخدام
              </a>
              <a href="#" className="text-gray-400 hover:text-primary-400 transition-colors">
                سياسة الأمان
              </a>
              <a href="#" className="text-gray-400 hover:text-primary-400 transition-colors">
                إخلاء المسؤولية
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* تحذير قانوني */}
      <div className="bg-gray-800 border-t border-gray-700">
        <div className="container-custom py-4">
          <div className="text-center text-xs text-gray-500">
            <p className="mb-2">
              <strong>تحذير:</strong> تداول العملات الرقمية ينطوي على مخاطر. يرجى التداول بحذر ومسؤولية.
            </p>
            <p>
              منصة إيكاروس P2P مرخصة ومنظمة وفقاً للقوانين المحلية. نحن ملتزمون بأعلى معايير الأمان والشفافية.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}