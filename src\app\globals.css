@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #1f2937;
  --primary: #3b82f6;
  --primary-dark: #1d4ed8;
  --success: #22c55e;
  --warning: #f59e0b;
  --danger: #ef4444;
  --border: #e5e7eb;
  --card: #ffffff;
  --muted: #f9fafb;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f1f5f9;
    --card: #1e293b;
    --muted: #334155;
    --border: #475569;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-cairo), 'Cairo', 'Tajawal', system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* دعم RTL */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .ltr {
  direction: ltr;
  text-align: left;
}

/* تحسينات للخطوط العربية */
.font-arabic {
  font-family: var(--font-cairo), 'Cairo', 'Tajawal', system-ui, sans-serif;
}

/* تأثيرات الحركة */
.animate-fade-in {
  animation: fadeIn 0.8s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

.animate-bounce-in {
  animation: bounceIn 0.8s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(40px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* تأثيرات التدرج */
.gradient-text {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-border {
  position: relative;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 12px;
  padding: 2px;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 2px;
  background: white;
  border-radius: 10px;
  z-index: -1;
}

/* تحسينات للأزرار */
.btn {
  @apply px-6 py-3 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 relative overflow-hidden;
}

.btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover:before {
  left: 100%;
}

.btn-primary {
  @apply bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 focus:ring-blue-500 shadow-lg hover:shadow-xl;
}

.btn-secondary {
  @apply bg-white text-gray-700 border-2 border-gray-300 hover:bg-gray-50 hover:border-gray-400 focus:ring-gray-500 shadow-sm hover:shadow-md;
}

.btn-success {
  @apply bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 focus:ring-green-500 shadow-lg hover:shadow-xl;
}

.btn-danger {
  @apply bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 focus:ring-red-500 shadow-lg hover:shadow-xl;
}

.btn-outline-primary {
  @apply border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white focus:ring-blue-500;
}

.btn-lg {
  @apply px-8 py-4 text-lg;
}

.btn-sm {
  @apply px-4 py-2 text-sm;
}

/* تحسينات للكروت */
.card {
  @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6 backdrop-blur-sm;
}

.card-hover {
  @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-2 hover:border-primary-200;
}

.card-gradient {
  @apply bg-gradient-to-br from-white to-gray-50;
}

.card-featured {
  @apply border-2 border-primary-200 bg-gradient-to-br from-primary-50 to-white relative;
}

.card-featured:before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 20px 20px 0;
  border-color: transparent #3b82f6 transparent transparent;
}

.card-featured:after {
  content: '⭐';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 10px;
  color: white;
}

/* تحسينات للنماذج */
.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* تحسينات للحالات */
.status-active {
  @apply bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium;
}

.status-pending {
  @apply bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium;
}

.status-cancelled {
  @apply bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium;
}

/* تحسينات للتخطيط */
.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.section-padding {
  @apply py-12 lg:py-20;
}

/* تحسينات للاستجابة */
@media (max-width: 640px) {
  .mobile-padding {
    @apply px-4;
  }
  
  .mobile-text {
    @apply text-sm;
  }
}

/* تحسينات للتمرير */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* تأثيرات التحميل */
.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

/* تحسينات للنصوص */
.text-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
